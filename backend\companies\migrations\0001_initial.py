# Generated by Django 5.1.5 on 2025-06-20 15:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('description', models.TextField()),
                ('industry', models.CharField(max_length=100)),
                ('size', models.CharField(max_length=100)),
                ('founded', models.CharField(max_length=4)),
                ('location', models.CharField(max_length=255)),
                ('website', models.URLField(max_length=255)),
                ('tier', models.Char<PERSON>ield(choices=[('Tier 1', 'Tier 1'), ('Tier 2', 'Tier 2'), ('Tier 3', 'Tier 3')], default='Tier 3', max_length=10)),
                ('campus_recruiting', models.BooleanField(default=False)),
                ('total_active_jobs', models.IntegerField(default=0)),
                ('total_applicants', models.IntegerField(default=0)),
                ('total_hired', models.IntegerField(default=0)),
                ('awaited_approval', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'ordering': ['name'],
            },
        ),
    ]
