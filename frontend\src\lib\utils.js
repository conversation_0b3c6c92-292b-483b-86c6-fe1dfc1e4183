import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

// Utility function to format job descriptions with proper line breaks and formatting
export function formatJobDescription(description) {
  if (!description) return "No description provided.";
  
  return description
    .replace(/\n/g, '<br />') // Convert newlines to HTML breaks
    .replace(/•/g, '•') // Ensure bullet points are preserved
    .replace(/\*\s/g, '• ') // Convert asterisk bullets to bullet symbols
    .replace(/-\s/g, '• ') // Convert dash bullets to bullet symbols
    .trim();
}

// Component for rendering formatted job descriptions
export function FormattedJobDescription({ description, className = "" }) {
  const formattedDescription = formatJobDescription(description);
  
  return (
    <div 
      className={`text-gray-700 leading-relaxed ${className}`}
      dangerouslySetInnerHTML={{ __html: formattedDescription }}
    />
  );
}
