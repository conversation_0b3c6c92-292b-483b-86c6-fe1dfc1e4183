// Centralized data source for jobs, companies, and applications
// This file will serve as the single source of truth for all job-related data

import { listCompanies, getCompany, transformCompanyData } from '../api/companies';

// Initial static data for companies
export const companies = [
  {
    id: 1,
    name: "Google",
    logo: "https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G",
    description: "A multinational technology company specializing in internet-related services and products.",
    industry: "Technology",
    size: "100,000+ employees",
    founded: "1998",
    location: "Mountain View, CA",
    website: "https://google.com",
    totalActiveJobs: 3,
    totalApplicants: 1250,
    totalHired: 89,
    awaitedApproval: 2,
    campus_recruiting: true,
    tier: "Tier 1"
  },
  {
    id: 2,
    name: "Microsoft",
    logo: "https://via.placeholder.com/48x48/00A1F1/FFFFFF?text=M",
    description: "A multinational technology corporation that develops computer software, consumer electronics, and personal computers.",
    industry: "Technology",
    size: "200,000+ employees",
    founded: "1975",
    location: "Seattle, WA",
    website: "https://microsoft.com",
    totalActiveJobs: 2,
    totalApplicants: 980,
    totalHired: 67,
    awaitedApproval: 1,
    campus_recruiting: true,
    tier: "Tier 1"
  },
  {
    id: 3,
    name: "Amazon",
    logo: "https://via.placeholder.com/48x48/FF9900/FFFFFF?text=A",
    description: "A multinational technology company focusing on e-commerce, cloud computing, and artificial intelligence.",
    industry: "E-commerce & Cloud",
    size: "1,500,000+ employees",
    founded: "1994",
    location: "Seattle, WA",
    website: "https://amazon.com",
    totalActiveJobs: 2,
    totalApplicants: 1450,
    totalHired: 112,
    awaitedApproval: 3,
    campus_recruiting: true,
    tier: "Tier 1"
  },
  {
    id: 4,
    name: "Apple",
    logo: "https://via.placeholder.com/48x48/007AFF/FFFFFF?text=A",
    description: "A multinational technology company that designs and develops consumer electronics, computer software, and online services.",
    industry: "Technology & Consumer Electronics",
    size: "165,000+ employees",
    founded: "1976",
    location: "Cupertino, CA",
    website: "https://apple.com",
    totalActiveJobs: 1,
    totalApplicants: 890,
    totalHired: 45,
    awaitedApproval: 1,
    campus_recruiting: true,
    tier: "Tier 1"
  },
  {
    id: 5,
    name: "Netflix",
    logo: "https://via.placeholder.com/48x48/E50914/FFFFFF?text=N",
    description: "A streaming entertainment service with over 200 million paid memberships in over 190 countries.",
    industry: "Entertainment & Media",
    size: "12,000+ employees",
    founded: "1997",
    location: "Los Gatos, CA",
    website: "https://netflix.com",
    totalActiveJobs: 1,
    totalApplicants: 567,
    totalHired: 23,
    awaitedApproval: 0,
    campus_recruiting: true,
    tier: "Tier 2"
  },
  {
    id: 6,
    name: "Cisco",
    logo: "https://via.placeholder.com/48x48/1BA0D7/FFFFFF?text=C",
    description: "A multinational digital communications technology conglomerate corporation headquartered in San Jose, California.",
    industry: "Networking & Security",
    size: "80,000+ employees",
    founded: "1984",
    location: "San Jose, CA",
    website: "https://cisco.com",
    totalActiveJobs: 1,
    totalApplicants: 445,
    totalHired: 34,
    awaitedApproval: 1,
    campus_recruiting: true,
    tier: "Tier 2"
  },
  {
    id: 7,
    name: "AI Forge",
    logo: "https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI",
    description: "A cutting-edge AI research and development company specializing in machine learning and artificial intelligence solutions.",
    industry: "Artificial Intelligence",
    size: "500+ employees",
    founded: "2018",
    location: "San Francisco, CA",
    website: "https://aiforge.com",
    totalActiveJobs: 3,
    totalApplicants: 234,
    totalHired: 18,
    awaitedApproval: 2,
    campus_recruiting: true,
    tier: "Tier 2"
  },
  {
    id: 8,
    name: "Cloudify",
    logo: "https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL",
    description: "A cloud infrastructure and DevOps automation company providing enterprise cloud orchestration solutions.",
    industry: "Cloud Computing",
    size: "300+ employees",
    founded: "2012",
    location: "Tel Aviv, Israel",
    website: "https://cloudify.co",
    totalActiveJobs: 3,
    totalApplicants: 189,
    totalHired: 15,
    awaitedApproval: 1,
    campus_recruiting: true,
    tier: "Tier 3"
  },
  {
    id: 9,
    name: "InnoWare",
    logo: "https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=IW",
    description: "An innovative software development company creating next-generation applications and platforms.",
    industry: "Software Development",
    size: "250+ employees",
    founded: "2015",
    location: "Austin, TX",
    website: "https://innovware.tech",
    totalActiveJobs: 2,
    totalApplicants: 156,
    totalHired: 12,
    awaitedApproval: 1,
    campus_recruiting: true,
    tier: "Tier 3"
  }
];

// Function to fetch companies from the API
export const fetchCompanies = async (params = {}) => {
  try {
    // Add cache busting parameter
    const fetchParams = { 
      ...params, 
      _t: new Date().getTime() 
    };
    
    console.log('Fetching companies with cache busting...');
    const response = await listCompanies(fetchParams);
    
    // Handle various response formats
    let companiesData = [];
    if (response.data && Array.isArray(response.data)) {
      companiesData = response.data;
    } else if (response.data && response.data.results && Array.isArray(response.data.results)) {
      companiesData = response.data.results;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      companiesData = response.data.data;
    }
    
    // Transform the data to match our frontend structure
    const transformedData = companiesData.map(transformCompanyData);
    
    console.log(`Fetched ${transformedData.length} companies from API`);
    
    // Only fall back to static data if we got nothing from the API
    if (transformedData.length === 0) {
      console.warn('API returned empty companies array, using static data');
      return companies;
    }
    
    return transformedData;
  } catch (error) {
    console.error('Error fetching companies:', error);
    
    // Try once more with a different endpoint format
    try {
      console.log('Trying alternate endpoint format...');
      const altResponse = await fetch('/api/v1/college/default-college/companies/');
      if (altResponse.ok) {
        const data = await altResponse.json();
        const altData = Array.isArray(data) ? data : (data.data || data.results || []);
        if (altData.length > 0) {
          console.log('Successfully retrieved companies from alternate endpoint');
          return altData.map(transformCompanyData);
        }
      }
    } catch (altError) {
      console.error('Alternate endpoint also failed:', altError);
    }
    
    // Return static data as final fallback
    return companies;
  }
};

// Function to get a company by ID
export const getCompanyById = async (id) => {
  try {
    // First try to get from API
    const response = await getCompany(id);
    return transformCompanyData(response.data);
  } catch (error) {
    console.error(`Error fetching company ${id}:`, error);
    // Fallback to static data
    return companies.find(company => company.id === id) || null;
  }
};

export const jobPostings = [
  {
    id: 1,
    title: "Software Engineering Intern",
    company_id: 1,
    company: "Google",
    logo: "https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G",
    location: "Mountain View, CA",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 8000,
    salary_max: 10000,
    per: "month",
    posted_date: "2024-01-15",
    deadline: "2024-03-15",
    description: "Join our engineering team to work on large-scale systems that impact billions of users worldwide. You'll collaborate with senior engineers on critical infrastructure projects.",
    requirements: [
      "Currently pursuing CS, EE, or related field",
      "Strong programming skills in Java, C++, or Python",
      "Understanding of data structures and algorithms",
      "GPA of 3.5 or higher"
    ],
    skills: ["Java", "Python", "Data Structures", "Algorithms", "System Design"],
    benefits: [
      "Competitive salary",
      "Housing stipend",
      "Free meals",
      "Mentorship program",
      "Return offer potential"
    ],
    remote_eligible: false,
    sponsorship_available: true,
    is_featured: true,
    is_active: true,
    on_campus: true
  },
  {
    id: 2,
    title: "Cloud Engineering Intern",
    company_id: 1,
    company: "Google",
    logo: "https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G",
    location: "Seattle, WA",
    type: "INTERNSHIP",
    mode: "Hybrid",
    duration: "10 weeks",
    salary_min: 7800,
    salary_max: 9500,
    per: "month",
    posted_date: "2024-01-20",
    deadline: "2024-03-20",
    description: "Work on Google Cloud Platform services and infrastructure. Build scalable solutions for enterprise customers.",
    requirements: [
      "Currently pursuing CS or related field",
      "Experience with cloud platforms (AWS, GCP, Azure)",
      "Knowledge of containerization (Docker, Kubernetes)",
      "Strong problem-solving skills"
    ],
    skills: ["Google Cloud Platform", "Kubernetes", "Docker", "Python", "Go"],
    benefits: [
      "Competitive compensation",
      "Cloud platform access",
      "Training and certifications",
      "Team collaboration",
      "Innovation time"
    ],
    remote_eligible: true,
    sponsorship_available: true,
    is_featured: false,
    is_active: true,
    on_campus: false
  },
  {
    id: 3,
    title: "Machine Learning Research Intern",
    company_id: 1,
    company: "Google",
    logo: "https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G",
    location: "Mountain View, CA",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "14 weeks",
    salary_min: 9000,
    salary_max: 11000,
    per: "month",
    posted_date: "2024-01-25",
    deadline: "2024-03-25",
    description: "Join Google Research to work on cutting-edge ML projects. Contribute to publications and open-source projects.",
    requirements: [
      "Currently pursuing MS/PhD in CS, ML, or related field",
      "Strong background in machine learning",
      "Experience with TensorFlow or PyTorch",
      "Research experience preferred"
    ],
    skills: ["Machine Learning", "TensorFlow", "PyTorch", "Python", "Research"],
    benefits: [
      "Research environment",
      "Publication opportunities",
      "Mentorship from experts",
      "Conference attendance",
      "Open source contributions"
    ],
    remote_eligible: false,
    sponsorship_available: true,
    is_featured: true,
    is_active: true,
    on_campus: true
  },
  {
    id: 4,
    title: "Data Science Intern",
    company_id: 2,
    company: "Microsoft",
    logo: "https://via.placeholder.com/48x48/00A1F1/FFFFFF?text=M",
    location: "Seattle, WA",
    type: "INTERNSHIP",
    mode: "Hybrid",
    duration: "10 weeks",
    salary_min: 7500,
    salary_max: 9000,
    per: "month",
    posted_date: "2024-01-12",
    deadline: "2024-02-28",
    description: "Work with our data science team to develop machine learning models that power Microsoft's cloud services and improve user experiences.",
    requirements: [
      "Currently pursuing MS/PhD in Data Science, Statistics, or related field",
      "Experience with Python, R, or SQL",
      "Knowledge of machine learning frameworks",
      "Strong analytical and problem-solving skills"
    ],
    skills: ["Python", "R", "SQL", "Machine Learning", "TensorFlow", "Azure"],
    benefits: [
      "Competitive compensation",
      "Relocation assistance",
      "Health benefits",
      "Professional development",
      "Networking opportunities"
    ],
    remote_eligible: true,
    sponsorship_available: true,
    is_featured: true,
    is_active: true,
    on_campus: true
  },
  {
    id: 5,
    title: "Azure Developer Intern",
    company_id: 2,
    company: "Microsoft",
    logo: "https://via.placeholder.com/48x48/00A1F1/FFFFFF?text=M",
    location: "Redmond, WA",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 7200,
    salary_max: 8800,
    per: "month",
    posted_date: "2024-01-18",
    deadline: "2024-03-10",
    description: "Develop and maintain Azure cloud services. Work on scalable solutions for enterprise customers worldwide.",
    requirements: [
      "Currently pursuing CS or related field",
      "Experience with cloud development",
      "Knowledge of C# or .NET",
      "Understanding of web services"
    ],
    skills: ["Azure", "C#", ".NET", "Cloud Computing", "REST APIs"],
    benefits: [
      "Azure credits",
      "Microsoft certifications",
      "Mentorship program",
      "Career development",
      "Innovation projects"
    ],
    remote_eligible: false,
    sponsorship_available: true,
    is_featured: false,
    is_active: true,
    on_campus: true
  },
  {
    id: 6,
    title: "Product Management Intern",
    company_id: 3,
    company: "Amazon",
    logo: "https://via.placeholder.com/48x48/FF9900/FFFFFF?text=A",
    location: "Austin, TX",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 6500,
    salary_max: 8000,
    per: "month",
    posted_date: "2024-01-10",
    deadline: "2024-03-01",
    description: "Drive product strategy and execution for Amazon's consumer-facing products. Work directly with engineering, design, and business teams.",
    requirements: [
      "Currently pursuing MBA or relevant undergraduate degree",
      "Strong analytical and communication skills",
      "Experience with product management tools",
      "Customer-obsessed mindset"
    ],
    skills: ["Product Strategy", "Analytics", "SQL", "A/B Testing", "User Research"],
    benefits: [
      "Competitive salary",
      "Stock options",
      "Professional mentorship",
      "Leadership training",
      "Full-time offer potential"
    ],
    remote_eligible: false,
    sponsorship_available: true,
    is_featured: false,
    is_active: true,
    on_campus: true
  },
  {
    id: 7,
    title: "AWS Solutions Architect Intern",
    company_id: 3,
    company: "Amazon",
    logo: "https://via.placeholder.com/48x48/FF9900/FFFFFF?text=A",
    location: "Arlington, VA",
    type: "INTERNSHIP",
    mode: "Hybrid",
    duration: "10 weeks",
    salary_min: 7000,
    salary_max: 8500,
    per: "month",
    posted_date: "2024-01-22",
    deadline: "2024-03-15",
    description: "Help customers architect and deploy cloud solutions on AWS. Work with enterprise clients on their cloud transformation journey.",
    requirements: [
      "Currently pursuing CS, IT, or related field",
      "Basic understanding of cloud computing",
      "Strong communication skills",
      "Interest in customer-facing roles"
    ],
    skills: ["AWS", "Cloud Architecture", "Solution Design", "Customer Relations"],
    benefits: [
      "AWS certifications",
      "Customer interaction",
      "Cloud expertise",
      "Professional growth",
      "Networking opportunities"
    ],
    remote_eligible: true,
    sponsorship_available: false,
    is_featured: false,
    is_active: true,
    on_campus: false
  },
  {
    id: 8,
    title: "UX Design Intern",
    company_id: 4,
    company: "Apple",
    logo: "https://via.placeholder.com/48x48/007AFF/FFFFFF?text=A",
    location: "Cupertino, CA",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 7000,
    salary_max: 8500,
    per: "month",
    posted_date: "2024-01-08",
    deadline: "2024-02-20",
    description: "Create innovative user experiences for Apple's next-generation products. Work alongside world-class designers and engineers.",
    requirements: [
      "Currently pursuing degree in Design, HCI, or related field",
      "Proficiency in Figma, Sketch, or similar tools",
      "Portfolio demonstrating design thinking",
      "Strong collaboration skills"
    ],
    skills: ["Figma", "Sketch", "Prototyping", "User Research", "Design Systems"],
    benefits: [
      "Competitive compensation",
      "Employee discounts",
      "Wellness programs",
      "Creative environment",
      "Career development"
    ],
    remote_eligible: false,
    sponsorship_available: false,
    is_featured: true,
    is_active: true,
    on_campus: true
  },
  {
    id: 9,
    title: "Marketing Analytics Intern",
    company_id: 5,
    company: "Netflix",
    logo: "https://via.placeholder.com/48x48/E50914/FFFFFF?text=N",
    location: "Los Gatos, CA",
    type: "INTERNSHIP",
    mode: "Hybrid",
    duration: "10 weeks",
    salary_min: 6000,
    salary_max: 7500,
    per: "month",
    posted_date: "2024-01-05",
    deadline: "2024-02-15",
    description: "Analyze marketing campaign performance and user engagement metrics to drive growth strategies for Netflix's global platform.",
    requirements: [
      "Currently pursuing degree in Marketing, Business, or Analytics",
      "Experience with Excel, SQL, and data visualization tools",
      "Strong quantitative analysis skills",
      "Interest in entertainment industry"
    ],
    skills: ["SQL", "Excel", "Tableau", "Google Analytics", "A/B Testing"],
    benefits: [
      "Competitive pay",
      "Netflix subscription",
      "Flexible hours",
      "Mentorship",
      "Networking events"
    ],
    remote_eligible: true,
    sponsorship_available: false,
    is_featured: false,
    is_active: true,
    on_campus: false
  },
  {
    id: 10,
    title: "Cybersecurity Intern",
    company_id: 6,
    company: "Cisco",
    logo: "https://via.placeholder.com/48x48/1BA0D7/FFFFFF?text=C",
    location: "San Jose, CA",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 6500,
    salary_max: 8000,
    per: "month",
    posted_date: "2024-01-03",
    deadline: "2024-02-28",
    description: "Join our security team to develop and implement cybersecurity solutions that protect enterprise networks and infrastructure.",
    requirements: [
      "Currently pursuing degree in Cybersecurity, CS, or related field",
      "Knowledge of network security protocols",
      "Understanding of threat analysis",
      "Security certifications preferred"
    ],
    skills: ["Network Security", "Python", "Linux", "Penetration Testing", "SIEM"],
    benefits: [
      "Competitive salary",
      "Security training",
      "Certification support",
      "Professional growth",
      "Team collaboration"
    ],
    remote_eligible: false,
    sponsorship_available: true,
    is_featured: false,
    is_active: true,
    on_campus: true
  },
  // AI Forge Jobs
  {
    id: 11,
    title: "Cloud Engineering Intern",
    company_id: 7,
    company: "AI Forge",
    logo: "https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI",
    location: "Hyderabad",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "16 weeks",
    salary_min: 10146,
    salary_max: 13914,
    per: "month",
    posted_date: "2025-05-20",
    deadline: "2025-07-25",
    description: "Join our cutting-edge cloud engineering team and work on scalable infrastructure solutions. You'll be working with modern technologies including Kubernetes, Docker, and cloud platforms to build the next generation of cloud applications.",
    requirements: [
      "Currently pursuing CS, IT, or related field",
      "Experience with cloud platforms",
      "Knowledge of containerization",
      "Strong programming skills"
    ],
    skills: ["PyTorch", "TensorFlow", "Kubernetes", "CI/CD", "Scrum"],
    benefits: [
      "Competitive stipend",
      "Technical mentorship",
      "Industry exposure",
      "Certification opportunities",
      "Full-time conversion possibility"
    ],
    remote_eligible: false,
    sponsorship_available: false,
    is_featured: false,
    is_active: false,
    on_campus: true
  },
  {
    id: 12,
    title: "AR/VR Developer Intern",
    company_id: 7,
    company: "AI Forge",
    logo: "https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI",
    location: "Mumbai",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 10022,
    salary_max: 13721,
    per: "month",
    posted_date: "2025-05-19",
    deadline: "2025-06-20",
    description: "Build immersive AR/VR experiences using cutting-edge technologies. Work on projects that blend physical and digital worlds.",
    requirements: [
      "Currently pursuing CS, Game Dev, or related field",
      "Experience with Unity or Unreal Engine",
      "Knowledge of 3D graphics",
      "Creative problem-solving skills"
    ],
    skills: ["Linux", "Angular", "Azure", "JIRA", "NLP", "AWS", "TypeScript", "Firebase"],
    benefits: [
      "Cutting-edge technology",
      "Creative projects",
      "Industry mentorship",
      "Portfolio development",
      "Innovation opportunities"
    ],
    remote_eligible: false,
    sponsorship_available: false,
    is_featured: false,
    is_active: false,
    on_campus: false
  },
  {
    id: 13,
    title: "UI/UX Designer Intern",
    company_id: 7,
    company: "AI Forge",
    logo: "https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI",
    location: "Hyderabad",
    type: "INTERNSHIP",
    mode: "Hybrid",
    duration: "14 weeks",
    salary_min: 13860,
    salary_max: 20446,
    per: "month",
    posted_date: "2025-05-22",
    deadline: "2025-06-30",
    description: "Design intuitive and beautiful user interfaces that delight users. Work on both web and mobile applications with a focus on user experience.",
    requirements: [
      "Currently pursuing Design, HCI, or related field",
      "Proficiency in design tools",
      "Understanding of UX principles",
      "Portfolio of design work"
    ],
    skills: ["Kotlin", "Jenkins", "Python", "Java", "NLP", "Kubernetes", "Swift"],
    benefits: [
      "Design mentorship",
      "Portfolio building",
      "User research exposure",
      "Creative freedom",
      "Industry connections"
    ],
    remote_eligible: true,
    sponsorship_available: false,
    is_featured: false,
    is_active: true,
    on_campus: false
  },
  // Cloudify Jobs
  {
    id: 14,
    title: "SEO Specialist Intern",
    company_id: 8,
    company: "Cloudify",
    logo: "https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL",
    location: "Hyderabad",
    type: "INTERNSHIP",
    mode: "Remote",
    duration: "10 weeks",
    salary_min: 8826,
    salary_max: 13341,
    per: "month",
    posted_date: "2025-05-21",
    deadline: "2025-07-20",
    description: "Drive organic growth through strategic SEO initiatives. Work with content teams to optimize website performance and implement data-driven SEO strategies.",
    requirements: [
      "Currently pursuing Marketing, Communications, or related field",
      "Basic understanding of SEO principles",
      "Experience with analytics tools",
      "Strong analytical skills"
    ],
    skills: ["GraphQL", "MongoDB", "Angular", "PostgreSQL", "Scrum", "Azure"],
    benefits: [
      "Remote work flexibility",
      "SEO expertise development",
      "Analytics training",
      "Marketing exposure",
      "Growth opportunities"
    ],
    remote_eligible: true,
    sponsorship_available: false,
    is_featured: false,
    is_active: false,
    on_campus: false
  },
  {
    id: 15,
    title: "Business Analyst Intern",
    company_id: 8,
    company: "Cloudify",
    logo: "https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL",
    location: "Bangalore",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 11333,
    salary_max: 14772,
    per: "month",
    posted_date: "2025-05-20",
    deadline: "2025-07-14",
    description: "Analyze business processes and drive data-driven decision making. Work directly with stakeholders to identify opportunities for improvement.",
    requirements: [
      "Currently pursuing Business, Economics, or related field",
      "Strong analytical and communication skills",
      "Experience with data analysis tools",
      "Business acumen"
    ],
    skills: ["MySQL", "Firebase", "Angular", "Python", "PyTorch", "MongoDB"],
    benefits: [
      "Business strategy exposure",
      "Stakeholder interaction",
      "Data analysis skills",
      "Process improvement",
      "Leadership development"
    ],
    remote_eligible: false,
    sponsorship_available: false,
    is_featured: false,
    is_active: false,
    on_campus: true
  },
  {
    id: 16,
    title: "Product Management Intern",
    company_id: 8,
    company: "Cloudify",
    logo: "https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL",
    location: "Mumbai",
    type: "INTERNSHIP",
    mode: "Hybrid",
    duration: "14 weeks",
    salary_min: 8729,
    salary_max: 15013,
    per: "month",
    posted_date: "2025-05-22",
    deadline: "2025-07-15",
    description: "Drive product strategy and work cross-functionally to deliver exceptional user experiences. Learn the fundamentals of product management.",
    requirements: [
      "Currently pursuing Business, CS, or related field",
      "Strong communication and analytical skills",
      "Interest in product development",
      "User-centric mindset"
    ],
    skills: ["Scrum", "Python", "Git", "Agile", "PostgreSQL"],
    benefits: [
      "Product strategy learning",
      "Cross-functional collaboration",
      "User research exposure",
      "Agile methodology",
      "Leadership skills"
    ],
    remote_eligible: true,
    sponsorship_available: false,
    is_featured: false,
    is_active: true,
    on_campus: true
  },
  // InnoWare Jobs
  {
    id: 17,
    title: "AI Research Intern",
    company_id: 9,
    company: "InnoWare",
    logo: "https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=IW",
    location: "Hyderabad",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "16 weeks",
    salary_min: 12242,
    salary_max: 16109,
    per: "month",
    posted_date: "2025-05-23",
    deadline: "2025-07-27",
    description: "Contribute to cutting-edge AI research projects and work with state-of-the-art machine learning technologies.",
    requirements: [
      "Currently pursuing MS/PhD in CS, AI, or related field",
      "Strong background in machine learning",
      "Research experience preferred",
      "Programming skills in Python"
    ],
    skills: ["Blockchain", "JIRA", "PostgreSQL", "Django", "Firebase"],
    benefits: [
      "Research opportunities",
      "AI expertise development",
      "Publication possibilities",
      "Innovation projects",
      "Academic collaboration"
    ],
    remote_eligible: false,
    sponsorship_available: false,
    is_featured: false,
    is_active: false,
    on_campus: true
  },
  {
    id: 18,
    title: "Robotics Intern",
    company_id: 9,
    company: "InnoWare",
    logo: "https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=IW",
    location: "Hyderabad",
    type: "INTERNSHIP",
    mode: "On-campus",
    duration: "12 weeks",
    salary_min: 12822,
    salary_max: 20721,
    per: "month",
    posted_date: "2025-05-22",
    deadline: "2025-07-04",
    description: "Work on innovative robotics projects and develop automation solutions that will shape the future of industry.",
    requirements: [
      "Currently pursuing Robotics, ME, CS, or related field",
      "Experience with robotics platforms",
      "Programming skills",
      "Hardware/software integration knowledge"
    ],
    skills: ["Django", "SEO", "Node.js", "Express", "Docker"],
    benefits: [
      "Hands-on robotics",
      "Innovation projects",
      "Hardware experience",
      "Automation expertise",
      "Industry applications"
    ],
    remote_eligible: false,
    sponsorship_available: false,
    is_featured: false,
    is_active: true,
    on_campus: false
  }
];

// Student applications data (for My Jobs page)
export const studentApplications = [
  {
    id: 1,
    job_id: 11, // AI Forge Cloud Engineering
    title: "Cloud Engineering Intern",
    company: "AI Forge",
    description: "Join our cutting-edge cloud engineering team and work on scalable infrastructure solutions.",
    location: "Hyderabad",
    job_type: "INTERNSHIP",
    salary_min: 10146,
    salary_max: 13914,
    required_skills: "PyTorch, TensorFlow, Kubernetes, CI/CD, Scrum",
    application_deadline: "2025-07-25",
    is_active: false,
    on_campus: true,
    status: "UNDER REVIEW",
    applied_at: "2025-05-22T14:57:47.186021",
    updated_at: "2025-05-28T14:57:47.186028"
  },
  {
    id: 2,
    job_id: 14, // Cloudify SEO Specialist
    title: "SEO Specialist Intern",
    company: "Cloudify",
    description: "Drive organic growth through strategic SEO initiatives.",
    location: "Hyderabad",
    job_type: "INTERNSHIP",
    salary_min: 8826,
    salary_max: 13341,
    required_skills: "GraphQL, MongoDB, Angular, PostgreSQL, Scrum, Azure",
    application_deadline: "2025-07-20",
    is_active: false,
    on_campus: false,
    status: "REJECTED",
    applied_at: "2025-05-23T14:57:47.186257",
    updated_at: "2025-05-28T14:57:47.186260"
  },
  {
    id: 3,
    job_id: 15, // Cloudify Business Analyst
    title: "Business Analyst Intern",
    company: "Cloudify",
    description: "Analyze business processes and drive data-driven decision making.",
    location: "Bangalore",
    job_type: "INTERNSHIP",
    salary_min: 11333,
    salary_max: 14772,
    required_skills: "MySQL, Firebase, Angular, Python, PyTorch, MongoDB",
    application_deadline: "2025-07-14",
    is_active: false,
    on_campus: true,
    status: "REJECTED",
    applied_at: "2025-05-23T14:57:47.186430",
    updated_at: "2025-05-28T14:57:47.186434"
  },
  {
    id: 4,
    job_id: 17, // InnoWare AI Research
    title: "AI Research Intern",
    company: "InnoWare",
    description: "Contribute to cutting-edge AI research projects.",
    location: "Hyderabad",
    job_type: "INTERNSHIP",
    salary_min: 12242,
    salary_max: 16109,
    required_skills: "Blockchain, JIRA, PostgreSQL, Django, Firebase",
    application_deadline: "2025-07-27",
    is_active: false,
    on_campus: true,
    status: "APPLIED",
    applied_at: "2025-05-25T14:57:47.186581",
    updated_at: "2025-05-28T14:57:47.186584"
  },
  {
    id: 5,
    job_id: 18, // InnoWare Robotics
    title: "Robotics Intern",
    company: "InnoWare",
    description: "Work on innovative robotics projects.",
    location: "Hyderabad",
    job_type: "INTERNSHIP",
    salary_min: 12822,
    salary_max: 20721,
    required_skills: "Django, SEO, Node.js, Express, Docker",
    application_deadline: "2025-07-04",
    is_active: true,
    on_campus: false,
    status: "APPLIED",
    applied_at: "2025-05-24T14:57:47.186683",
    updated_at: "2025-05-28T14:57:47.186686"
  },
  {
    id: 6,
    job_id: 12, // AI Forge AR/VR
    title: "AR/VR Developer Intern",
    company: "AI Forge",
    description: "Build immersive AR/VR experiences using cutting-edge technologies.",
    location: "Mumbai",
    job_type: "INTERNSHIP",
    salary_min: 10022,
    salary_max: 13721,
    required_skills: "Linux, Angular, Azure, JIRA, NLP, AWS, TypeScript, Firebase",
    application_deadline: "2025-06-20",
    is_active: false,
    on_campus: false,
    status: "INTERVIEW SCHEDULED",
    applied_at: "2025-05-21T14:57:47.186778",
    updated_at: "2025-05-28T14:57:47.186781"
  },
  {
    id: 7,
    job_id: 16, // Cloudify Product Management
    title: "Product Management Intern",
    company: "Cloudify",
    description: "Drive product strategy and work cross-functionally to deliver exceptional user experiences.",
    location: "Mumbai",
    job_type: "INTERNSHIP",
    salary_min: 8729,
    salary_max: 15013,
    required_skills: "Scrum, Python, Git, Agile, PostgreSQL",
    application_deadline: "2025-07-15",
    is_active: true,
    on_campus: true,
    status: "INTERVIEW SCHEDULED",
    applied_at: "2025-05-24T14:57:47.187017",
    updated_at: "2025-05-28T14:57:47.187023"
  },
  {
    id: 8,
    job_id: 13, // AI Forge UI/UX
    title: "UI/UX Designer Intern",
    company: "AI Forge",
    description: "Design intuitive and beautiful user interfaces that delight users.",
    location: "Hyderabad",
    job_type: "INTERNSHIP",
    salary_min: 13860,
    salary_max: 20446,
    required_skills: "Kotlin, Jenkins, Python, Java, NLP, Kubernetes, Swift",
    application_deadline: "2025-06-30",
    is_active: true,
    on_campus: false,
    status: "INTERVIEW SCHEDULED",
    applied_at: "2025-05-24T14:57:47.187473",
    updated_at: "2025-05-28T14:57:47.187476"
  }
];

// Helper functions
export const getActiveJobs = () => {
  return jobPostings.filter(job => job.is_active);
};

export const getJobById = (jobId) => {
  return jobPostings.find(job => job.id === jobId);
};

export const getCompaniesWithActiveJobs = () => {
  const activeJobCompanyIds = new Set(
    jobPostings.filter(job => job.is_active).map(job => job.company_id)
  );
  return companies.filter(company => activeJobCompanyIds.has(company.id));
};

export const getApplicationStats = () => {
  const total = studentApplications.length;
  const pending = studentApplications.filter(app => 
    app.status === 'APPLIED' || app.status === 'UNDER REVIEW'
  ).length;
  const interviews = studentApplications.filter(app => 
    app.status === 'INTERVIEW SCHEDULED'
  ).length;
  const rejected = studentApplications.filter(app => 
    app.status === 'REJECTED'
  ).length;
  const accepted = studentApplications.filter(app => 
    app.status === 'ACCEPTED'
  ).length;

  return { total, pending, interviews, rejected, accepted };
};

export const getJobStats = () => {
  const total = jobPostings.length;
  const active = jobPostings.filter(job => job.is_active).length;
  const internships = jobPostings.filter(job => job.type === 'INTERNSHIP').length;
  const fullTime = jobPostings.filter(job => job.type === 'FULL_TIME').length;
  const remote = jobPostings.filter(job => job.remote_eligible).length;
  const featured = jobPostings.filter(job => job.is_featured).length;

  return { total, active, internships, fullTime, remote, featured };
};

// Function to get jobs by company
export function getJobsByCompany(companyId) {
  // This is a placeholder implementation
  // You should replace this with an actual API call to fetch jobs by company ID
  
  // For now, we'll return an empty array to prevent the error
  console.log(`Fetching jobs for company ID: ${companyId}`);
  return [];
  
  // When you have the API endpoint ready, implement something like:
  // return client.get(`/api/v1/companies/${companyId}/jobs/`)
  //   .then(response => response.data)
  //   .catch(error => {
  //     console.error(`Error fetching jobs for company ${companyId}:`, error);
  //     return [];
  //   });
}