'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CompanyCard, JobTable, CompanyDetailsModal, JobFilters, PaginatedList } from '../../../components/ui/Sidebar';
import { IconSearch, IconMenu2, IconX } from '@tabler/icons-react';
import { listJobs, listJobsAdmin, listCompanies, createJob, toggleJobPublish } from '../../../api/jobs';
import JobPostingForm from '../../../components/JobPostingForm';

export default function Jobs() {
  const router = useRouter();
  const [selectedMenu, setSelectedMenu] = useState('Companies');
  const [companies, setCompanies] = useState([]);
  const [jobListings, setJobListings] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [typeFilter, setTypeFilter] = useState('All');
  const [minCTC, setMinCTC] = useState('');
  const [maxCTC, setMaxCTC] = useState('');
  const [minStipend, setMinStipend] = useState('');
  const [maxStipend, setMaxStipend] = useState('');
  const [deadlineFilter, setDeadlineFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showJobForm, setShowJobForm] = useState(false);

  const menuItems = [
    { label: 'Companies', value: 'companies' },
    { label: 'Job Listing', value: 'job listing' },
    { label: 'Post a Job', value: 'post a job' }
  ];

  // Fetch companies and jobs from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch both companies and jobs in parallel
        const [companiesResponse, jobsResponse] = await Promise.all([
          listCompanies().catch(() => ({ data: [] })), // Fallback to empty array if companies API fails
          listJobsAdmin().catch(() => ({ data: [] })) // Use admin endpoint to see all jobs
        ]);

        // Extract data from responses - admin endpoint returns {data: [...]} or {data: {data: [...], pagination: {...}}}
        const companiesData = Array.isArray(companiesResponse.data) ? companiesResponse.data : [];
        const jobsData = Array.isArray(jobsResponse.data?.data) ? jobsResponse.data.data : 
                        Array.isArray(jobsResponse.data) ? jobsResponse.data : [];



        // Transform jobs data to include company information
        const jobsWithCompanyInfo = jobsData.map(job => ({
          ...job,
          companyName: job.company_name,
          title: job.title,
          type: job.job_type,
          ctc: job.salary_max || 0,
          stipend: job.job_type === 'INTERNSHIP' ? (job.salary_max || 0) : 0,
          deadline: job.application_deadline
        }));

        // If companies API is available, use it; otherwise create companies from jobs data
        if (companiesData.length > 0) {
          setCompanies(companiesData);
        } else {
          // Create companies from unique companies in jobs data
          const uniqueCompanies = [...new Set(jobsData.map(job => job.company_name))];
          const companiesFromJobs = uniqueCompanies.map((companyName, index) => {
            const companyJobs = jobsData.filter(job => job.company_name === companyName);
            return {
              id: index + 1,
              companyName: companyName,
              company_name: companyName,
              companyDescription: `${companyName} - Job opportunities available`,
              location: companyJobs[0]?.location || 'Location not specified',
              employeeCount: 'N/A',
              website: '#',
              awaitedApproval: 0,
              totalApplicants: 0,
              totalHired: 0,
              totalActiveJobs: companyJobs.length,
              activeListingsData: companyJobs.map(job => ({
                title: job.title,
                type: job.job_type,
                ctc: job.salary_max || 0,
                stipend: job.job_type === 'INTERNSHIP' ? (job.salary_max || 0) : 0,
                deadline: job.application_deadline
              }))
            };
          });
          setCompanies(companiesFromJobs);
        }

        setJobListings(jobsWithCompanyInfo);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load data. Please try again.');
        setCompanies([]);
        setJobListings([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleMenuClick = (item) => {
    setSelectedMenu(item.label);
    setIsMobileSidebarOpen(false); // Close mobile sidebar after selection
    if (item.label === 'Post a Job') {
      setShowJobForm(false); // Reset form state when switching to Post a Job
    }
  };

  const handleTogglePublish = async (jobId, currentStatus) => {
    try {
      await toggleJobPublish(jobId);
      
      // Refresh the job listings
      const jobsResponse = await listJobsAdmin();
      const jobsData = Array.isArray(jobsResponse.data?.data) ? jobsResponse.data.data : 
                      Array.isArray(jobsResponse.data) ? jobsResponse.data : [];
      const jobsWithCompanyInfo = jobsData.map(job => ({
        ...job,
        companyName: job.company_name,
        title: job.title,
        type: job.job_type,
        ctc: job.salary_max || 0,
        stipend: job.job_type === 'INTERNSHIP' ? (job.salary_max || 0) : 0,
        deadline: job.application_deadline
      }));
      setJobListings(jobsWithCompanyInfo);
      
      const newStatus = !currentStatus;
      alert(`Job ${newStatus ? 'published' : 'unpublished'} successfully!`);
    } catch (error) {
      console.error('Error toggling job publish status:', error);
      alert('Failed to update job status. Please try again.');
    }
  };

  const handleCreateJob = async (jobData) => {
    try {
      // Format the data according to what the backend expects
      const formattedJobData = {
        title: jobData.title,
        description: jobData.description,
        location: jobData.location,
        job_type: jobData.job_type || 'FULL_TIME',
        salary_min: parseFloat(jobData.salary_min) || 0,
        salary_max: parseFloat(jobData.salary_max) || 0,
        required_skills: Array.isArray(jobData.requirements) 
          ? jobData.requirements.join(', ') 
          : jobData.required_skills || '',
        application_deadline: jobData.application_deadline || jobData.deadline,
        is_active: jobData.is_active !== undefined ? jobData.is_active : true
      };
      
      await createJob(formattedJobData);
      alert('Job posting created successfully!');
      setShowJobForm(false);
      // Refresh the job listings
      const jobsResponse = await listJobsAdmin();
      const jobsData = Array.isArray(jobsResponse.data?.data) ? jobsResponse.data.data : 
                      Array.isArray(jobsResponse.data) ? jobsResponse.data : [];
      const jobsWithCompanyInfo = jobsData.map(job => ({
        ...job,
        companyName: job.company_name,
        title: job.title,
        type: job.job_type,
        ctc: job.salary_max || 0,
        stipend: job.job_type === 'INTERNSHIP' ? (job.salary_max || 0) : 0,
        deadline: job.application_deadline
      }));
      setJobListings(jobsWithCompanyInfo);
    } catch (error) {
      console.error('Error creating job:', error);
      throw error; // Re-throw to let the form handle it
    }
  };

  const filteredCompanies = companies
    .filter(company =>
      (company.companyName || company.company_name || '').toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => (a.companyName || a.company_name || '').localeCompare(b.companyName || b.company_name || ''));

  const filteredJobs = jobListings
    .filter(job => {
      const matchesTitle = (job.title || '').toLowerCase().includes(searchQuery.toLowerCase());
      const matchesType = typeFilter === 'All' || job.type?.toLowerCase() === typeFilter.toLowerCase() || job.job_type?.toLowerCase() === typeFilter.toLowerCase();
      const matchesCTC = (!minCTC || job.ctc >= parseInt(minCTC)) && (!maxCTC || job.ctc <= parseInt(maxCTC));
      const matchesStipend = (!minStipend || job.stipend >= parseInt(minStipend)) && (!maxStipend || job.stipend <= parseInt(maxStipend));
      const matchesDeadline = !deadlineFilter || new Date(job.deadline || job.application_deadline) <= new Date(deadlineFilter);

      return (
        matchesTitle &&
        matchesType &&
        matchesCTC &&
        matchesStipend &&
        matchesDeadline
      );
    })
    .sort((a, b) => (a.companyName || a.company_name || '').toLowerCase().localeCompare((b.companyName || b.company_name || '').toLowerCase()));



  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-700 mb-2">Error Loading Data</h3>
          <p className="text-sm text-gray-500 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Job Management</h1>
          <p className="text-sm text-gray-600">Manage companies and job listings</p>
        </div>
        
        {/* Mobile menu button */}
        <button
          className="md:hidden p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
          onClick={() => setIsMobileSidebarOpen(true)}
        >
          <IconMenu2 className="w-5 h-5" />
        </button>
      </div>

      {/* Main Content Layout */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 flex-1 overflow-hidden">
        <div className="flex h-full relative">
          {/* Desktop Secondary Navigation Sidebar */}
          <div className="hidden md:block w-64 border-r border-gray-200 flex-shrink-0">
            <div className="p-4 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-800">Sections</h3>
              <p className="text-sm text-gray-500">Navigate through options</p>
            </div>
            <nav className="p-4">
              <ul className="space-y-2">
                {menuItems.map((item, index) => (
                  <li key={index}>
                    <button
                      className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                        selectedMenu === item.label
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                      onClick={() => handleMenuClick(item)}
                    >
                      {item.label}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Mobile Secondary Navigation Overlay */}
          {isMobileSidebarOpen && (
            <div className="md:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
              <div className="absolute left-0 top-0 h-full w-80 bg-white shadow-xl">
                <div className="p-4 border-b border-gray-100 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Sections</h3>
                    <p className="text-sm text-gray-500">Navigate through options</p>
                  </div>
                  <button
                    onClick={() => setIsMobileSidebarOpen(false)}
                    className="p-2 rounded-lg hover:bg-gray-100"
                  >
                    <IconX className="w-5 h-5" />
                  </button>
                </div>
                <nav className="p-4">
                  <ul className="space-y-2">
                    {menuItems.map((item, index) => (
                      <li key={index}>
                        <button
                          className={`w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                            selectedMenu === item.label
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                          }`}
                          onClick={() => handleMenuClick(item)}
                        >
                          {item.label}
                        </button>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          )}

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col min-h-0">
            {/* Search Bar - Fixed at top when applicable */}
            {selectedMenu === 'Companies' && (
              <div className="p-4 md:p-6 border-b border-gray-100 flex-shrink-0">
                <div className="flex items-center justify-between mb-4 md:hidden">
                  <h2 className="text-lg font-semibold text-gray-800">{selectedMenu}</h2>
                </div>
                <div className="relative max-w-md">
                  <IconSearch
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                    size={18}
                  />
                  <input
                    type="text"
                    placeholder="Search companies..."
                    className="w-full pl-10 pr-4 py-3 rounded-lg bg-gray-50 border border-gray-200 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            )}
            {selectedMenu === 'Job Listing' && (
              <div className="p-4 md:p-6 border-b border-gray-100 flex-shrink-0">
                <div className="relative w-full flex flex-wrap md:flex-nowrap items-center gap-4">
                  {/* Search Bar */}
                  <div className="relative flex-grow max-w-md">
                    <IconSearch
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                      size={18}
                    />
                    <input
                      type="text"
                      placeholder="Search job titles..."
                      className="w-full pl-10 pr-4 py-3 rounded-lg bg-gray-50 border border-gray-200 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  {/* Filters - Aligned on same row */}
                  <JobFilters
                    typeFilter={typeFilter}
                    setTypeFilter={setTypeFilter}
                    minCTC={minCTC}
                    setMinCTC={setMinCTC}
                    maxCTC={maxCTC}
                    setMaxCTC={setMaxCTC}
                    minStipend={minStipend}
                    setMinStipend={setMinStipend}
                    maxStipend={maxStipend}
                    setMaxStipend={setMaxStipend}
                    deadlineFilter={deadlineFilter}
                    setDeadlineFilter={setDeadlineFilter}
                  />
                </div>
              </div>
            )}
            {/* Mobile section header for non-search sections */}
            {selectedMenu !== 'Companies' && selectedMenu !== 'Job Listing' && (
              <div className="p-4 border-b border-gray-100 md:hidden flex-shrink-0">
                <h2 className="text-lg font-semibold text-gray-800">{selectedMenu}</h2>
              </div>
            )}

            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-auto p-4 md:p-6">
              {/* Content Based on Menu Selection */}
              {selectedMenu === 'Companies' ? (
                <div className="space-y-6">
                    {filteredCompanies.length > 0 ? (
                      <PaginatedList
                      items={filteredCompanies}
                      searchQuery={searchQuery}
                      renderItem={(company, index) => (
                        <CompanyCard key={index} company={company} onClick={setSelectedCompany} />
                      )}
                    />
                    ) : (
                      <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                        <div className="text-gray-400 mb-4">
                          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No companies found</h3>
                        <p className="text-sm text-gray-400">Try adjusting your search criteria</p>
                      </div>
                    )}
                  </div>
              ) : selectedMenu === 'Job Listing' ? (
                <div className="space-y-6">
                  <div className="overflow-x-auto">
                    <div className="space-y-4">
                    <PaginatedList
                      items={filteredJobs}
                      itemsPerPage={15}
                      searchQuery={searchQuery}
                      renderItem={(jobsChunk, index) => (
                        <JobTable 
                          key={index} 
                          jobs={jobsChunk} 
                          onTogglePublish={handleTogglePublish}
                        />
                      )}
                      chunkItems={true}
                    />
                    </div>
                  </div>
                </div>
              ) : showJobForm ? (
                <JobPostingForm
                  companies={companies}
                  onSubmit={handleCreateJob}
                  onCancel={() => setShowJobForm(false)}
                />
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="bg-blue-50 rounded-full p-4 mb-4">
                    <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-700 mb-2">Post a Job</h3>
                  <p className="text-sm text-gray-500 mb-4 max-w-sm">Create and manage job postings for your organization</p>
                  <button 
                    onClick={() => setShowJobForm(true)}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Create New Job Post
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Company Details Modal */}
      {selectedCompany && (
        <CompanyDetailsModal
          company={selectedCompany}
          onClose={() => setSelectedCompany(null)}
        />
      )}
    </div>
  );
}




