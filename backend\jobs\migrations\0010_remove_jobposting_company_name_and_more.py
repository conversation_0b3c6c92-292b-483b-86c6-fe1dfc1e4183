# Generated by Django 5.1.6 on 2025-06-23 13:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0003_companyfollower'),
        ('jobs', '0009_jobposting_company_jobposting_company_name_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='jobposting',
            name='company_name',
        ),
        migrations.RemoveField(
            model_name='jobposting',
            name='employer',
        ),
        migrations.AlterField(
            model_name='jobposting',
            name='company',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='job_postings', to='companies.company'),
            preserve_default=False,
        ),
    ]
