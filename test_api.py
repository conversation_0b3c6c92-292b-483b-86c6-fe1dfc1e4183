#!/usr/bin/env python3
"""
Simple script to test the student profile API endpoints
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_endpoints():
    print("Testing Student Profile API Endpoints")
    print("=" * 50)
    
    # Test 1: Check if ViewSet endpoint exists
    print("\n1. Testing ViewSet endpoint (GET /api/accounts/profiles/)")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts/profiles/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"Error: {e}")

    # Test 1b: Check auth endpoint
    print("\n1b. Testing auth ViewSet endpoint (GET /api/auth/profiles/)")
    try:
        response = requests.get(f"{BASE_URL}/api/auth/profiles/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Check if individual profile endpoint exists
    print("\n2. Testing individual profile endpoint (GET /api/accounts/profiles/1/)")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts/profiles/1/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Check if update endpoint exists
    print("\n3. Testing update endpoint (GET /api/accounts/students/1/update/)")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts/students/1/update/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: Check allowed methods for ViewSet
    print("\n4. Testing OPTIONS on ViewSet endpoint")
    try:
        response = requests.options(f"{BASE_URL}/api/accounts/profiles/1/")
        print(f"Status: {response.status_code}")
        print(f"Allowed methods: {response.headers.get('Allow', 'Not specified')}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 5: Check allowed methods for update endpoint
    print("\n5. Testing OPTIONS on update endpoint")
    try:
        response = requests.options(f"{BASE_URL}/api/accounts/students/1/update/")
        print(f"Status: {response.status_code}")
        print(f"Allowed methods: {response.headers.get('Allow', 'Not specified')}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_endpoints()
