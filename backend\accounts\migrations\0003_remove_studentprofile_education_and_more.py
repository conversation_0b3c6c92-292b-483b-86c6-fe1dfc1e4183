# Generated by Django 5.2 on 2025-05-15 09:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_alter_user_user_type_delete_employerprofile'),
        ('college', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='studentprofile',
            name='education',
        ),
        migrations.RemoveField(
            model_name='studentprofile',
            name='skills',
        ),
        migrations.RemoveField(
            model_name='studentprofile',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='branch',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='college',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='college.college'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='contact_email',
            field=models.EmailField(default='<EMAIL>', max_length=254),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='gpa',
            field=models.CharField(default='0.0', max_length=10),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='student_id',
            field=models.CharField(default='TEMP001', max_length=100),
        ),
        migrations.AddField(
            model_name='user',
            name='college',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='college.college'),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='first_name',
            field=models.CharField(default='John', max_length=100),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='last_name',
            field=models.CharField(default='Doe', max_length=100),
        ),
    ]
