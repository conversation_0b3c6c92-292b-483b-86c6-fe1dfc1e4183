# Generated by Django 5.2 on 2025-05-22 05:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0005_studentprofile_updated_at'),
    ]

    operations = [
        migrations.AlterField(
            model_name='studentprofile',
            name='contact_email',
            field=models.EmailField(blank=True, default='<EMAIL>', max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='first_name',
            field=models.CharField(default='..', max_length=100),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='last_name',
            field=models.CharField(default='..', max_length=100),
        ),
        migrations.CreateModel(
            name='EmployerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=255)),
                ('industry', models.CharField(blank=True, max_length=255)),
                ('website', models.URLField(blank=True)),
                ('contact_email', models.EmailField(max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employer_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
