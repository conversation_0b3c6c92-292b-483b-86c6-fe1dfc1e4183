"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_api_companies_js";
exports.ids = ["_ssr_src_api_companies_js"];
exports.modules = {

/***/ "(ssr)/./src/api/companies.js":
/*!******************************!*\
  !*** ./src/api/companies.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFollowingStatus: () => (/* binding */ checkFollowingStatus),\n/* harmony export */   createCompany: () => (/* binding */ createCompany),\n/* harmony export */   deleteCompany: () => (/* binding */ deleteCompany),\n/* harmony export */   fetchCompanies: () => (/* binding */ fetchCompanies),\n/* harmony export */   followCompany: () => (/* binding */ followCompany),\n/* harmony export */   getCompany: () => (/* binding */ getCompany),\n/* harmony export */   getCompanyStats: () => (/* binding */ getCompanyStats),\n/* harmony export */   getFollowersCount: () => (/* binding */ getFollowersCount),\n/* harmony export */   getIndustries: () => (/* binding */ getIndustries),\n/* harmony export */   getUserFollowedCompanies: () => (/* binding */ getUserFollowedCompanies),\n/* harmony export */   listCompanies: () => (/* binding */ listCompanies),\n/* harmony export */   transformCompanyData: () => (/* binding */ transformCompanyData),\n/* harmony export */   unfollowCompany: () => (/* binding */ unfollowCompany),\n/* harmony export */   updateCompany: () => (/* binding */ updateCompany),\n/* harmony export */   uploadCompanyLogo: () => (/* binding */ uploadCompanyLogo)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/api/client.js\");\n\n// List all companies with optional filtering\nfunction listCompanies(params = {}) {\n    const queryParams = new URLSearchParams();\n    // Add pagination parameters\n    if (params.page) queryParams.append('page', params.page);\n    if (params.per_page) queryParams.append('per_page', params.per_page);\n    // Add filtering parameters\n    if (params.tier && params.tier !== 'ALL') queryParams.append('tier', params.tier);\n    if (params.industry && params.industry !== 'ALL') queryParams.append('industry', params.industry);\n    if (params.campus_recruiting) queryParams.append('campus_recruiting', params.campus_recruiting);\n    if (params.search) queryParams.append('search', params.search);\n    if (params.sort) queryParams.append('sort', params.sort);\n    // Add cache busting parameter to prevent cached responses\n    queryParams.append('_t', new Date().getTime());\n    const queryString = queryParams.toString();\n    // Try both endpoints to maximize compatibility with backend\n    const urls = [\n        `/api/v1/companies/${queryString ? `?${queryString}` : ''}`,\n        `/api/v1/college/default-college/companies/${queryString ? `?${queryString}` : ''}`\n    ];\n    // Try primary endpoint first, fall back to secondary if needed\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[0]).catch((error)=>{\n        console.log(`Primary endpoint failed: ${error.message}, trying fallback...`);\n        return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[1]);\n    });\n}\n// Function to fetch companies from the API with improved reliability\nasync function fetchCompanies(params = {}) {\n    try {\n        console.log('Fetching companies from API...');\n        const response = await listCompanies(params);\n        // Transform the data to match our frontend structure\n        let companies = [];\n        if (response.data && Array.isArray(response.data)) {\n            companies = response.data;\n        } else if (response.data && response.data.results && Array.isArray(response.data.results)) {\n            companies = response.data.results;\n        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n            companies = response.data.data;\n        }\n        console.log(`Retrieved ${companies.length} companies from API`);\n        // Only proceed with detail fetching if we got a reasonable number of companies\n        if (companies.length > 0) {\n            // For each company in the list, fetch complete details to get all fields\n            const detailedCompanies = await Promise.all(companies.map(async (company)=>{\n                try {\n                    // Fetch detailed info for each company\n                    const detailResponse = await getCompany(company.id);\n                    return transformCompanyData(detailResponse.data);\n                } catch (error) {\n                    console.log(`Could not fetch details for company ${company.id}:`, error);\n                    // Fall back to the list data if detail fetch fails\n                    return transformCompanyData(company);\n                }\n            }));\n            // Store companies in sessionStorage for quick access\n            if (false) {}\n            return detailedCompanies;\n        } else {\n            throw new Error('No companies returned from API');\n        }\n    } catch (error) {\n        console.error('Error fetching companies from API:', error);\n        // Check if we have cached data in sessionStorage\n        if (false) {}\n        // Import the static data as last resort\n        console.log('Falling back to static company data');\n        const { companies } = await __webpack_require__.e(/*! import() */ \"_ssr_src_data_jobsData_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../data/jobsData */ \"(ssr)/./src/data/jobsData.js\"));\n        return companies;\n    }\n}\n// Get a single company by ID with better error handling\nfunction getCompany(companyId) {\n    // Try both possible endpoints\n    const urls = [\n        `/api/v1/company/${companyId}/`,\n        `/api/v1/companies/${companyId}/`,\n        `/api/v1/college/default-college/companies/${companyId}/`\n    ];\n    // Try each URL in sequence until one works\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[0]).catch((error1)=>{\n        console.log(`First company endpoint failed: ${error1.message}, trying second...`);\n        return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[1]).catch((error2)=>{\n            console.log(`Second company endpoint failed: ${error2.message}, trying third...`);\n            return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[2]);\n        });\n    });\n}\n// Create a new company\nfunction createCompany(companyData) {\n    const formData = new FormData();\n    // Append all fields to the FormData\n    Object.keys(companyData).forEach((key)=>{\n        // Handle file upload for logo\n        if (key === 'logo' && companyData[key] instanceof File) {\n            formData.append(key, companyData[key]);\n        } else if (companyData[key] !== null && companyData[key] !== undefined) {\n            formData.append(key, companyData[key]);\n        }\n    });\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/v1/companies/', formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n}\n// Update company details\nfunction updateCompany(companyId, companyData) {\n    const formData = new FormData();\n    // Append all fields to the FormData\n    Object.keys(companyData).forEach((key)=>{\n        // Handle file upload for logo\n        if (key === 'logo' && companyData[key] instanceof File) {\n            formData.append(key, companyData[key]);\n        } else if (companyData[key] !== null && companyData[key] !== undefined) {\n            formData.append(key, companyData[key]);\n        }\n    });\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/api/v1/companies/${companyId}/`, formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n}\n// Delete a company\nfunction deleteCompany(companyId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/v1/companies/${companyId}/`);\n}\n// Upload company logo\nfunction uploadCompanyLogo(companyId, logoFile) {\n    const formData = new FormData();\n    formData.append('logo', logoFile);\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/api/v1/companies/${companyId}/upload-logo/`, formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n}\n// Get company statistics\nfunction getCompanyStats() {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/companies/stats/');\n}\n// Get unique industries\nfunction getIndustries() {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/companies/industries/');\n}\n// Transform backend company data to match frontend structure\nfunction transformCompanyData(backendData) {\n    return {\n        id: backendData.id,\n        name: backendData.name,\n        logo: backendData.logo || `https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${backendData.name.charAt(0)}`,\n        description: backendData.description || '',\n        industry: backendData.industry || '',\n        size: backendData.size || 'Size not specified',\n        founded: backendData.founded || '',\n        location: backendData.location || 'Location not specified',\n        website: backendData.website || '',\n        tier: backendData.tier || 'Tier 3',\n        campus_recruiting: backendData.campus_recruiting || false,\n        totalActiveJobs: backendData.total_active_jobs || 0,\n        totalApplicants: backendData.total_applicants || 0,\n        totalHired: backendData.total_hired || 0,\n        awaitedApproval: backendData.awaited_approval || 0\n    };\n}\n// Get followers count for a company\nfunction getFollowersCount(companyId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/v1/companies/${companyId}/followers/count/`);\n}\n// Follow a company\nfunction followCompany(companyId, userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/api/v1/companies/${companyId}/followers/`, {\n        user_id: userId\n    });\n}\n// Unfollow a company\nfunction unfollowCompany(companyId, userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/v1/companies/${companyId}/followers/`, {\n        data: {\n            user_id: userId\n        }\n    });\n}\n// Check if user is following a company\nfunction checkFollowingStatus(companyId, userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/v1/companies/${companyId}/followers/status/?user_id=${userId}`);\n}\n// Get all companies a user is following\nfunction getUserFollowedCompanies(userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/v1/users/${userId}/following/`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/companies.js\n");

/***/ })

};
;