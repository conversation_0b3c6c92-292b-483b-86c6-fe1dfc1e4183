import { GraduationCap } from "lucide-react";

export default function DepartmentCards({ departmentOptions, departmentStats, allStudents, onSelect }) {
  return (
    <>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Student Management</h1>
        <p className="text-gray-600">Select a department to view students</p>
      </div>
      {/* Summary Stats */}
      <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{allStudents.length}</div>
            <div className="text-sm text-gray-600">Total Students</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{departmentStats.length}</div>
            <div className="text-sm text-gray-600">Departments</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {[...new Set(allStudents.map(s => s.year).filter(y => y && y !== 'N/A'))].length}
            </div>
            <div className="text-sm text-gray-600">Active Years</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {allStudents.filter(s => s.cgpa !== 'N/A' && parseFloat(s.cgpa) >= 8.0).length}
            </div>
            <div className="text-sm text-gray-600">High Performers</div>
          </div>
        </div>
      </div>
      {/* Department Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {departmentOptions.map((dept) => {
          const stats = departmentStats.find(stat => stat.department === dept.value);
          const studentCount = stats ? stats.count : 0;
          return (
            <div
              key={dept.value}
              onClick={() => onSelect(dept.value)}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200 group"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors">
                  <GraduationCap className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{studentCount}</div>
                  <div className="text-xs text-gray-500">Students</div>
                </div>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{dept.label}</h3>
              <p className="text-sm text-gray-600">View and manage {dept.label.toLowerCase()} students</p>
            </div>
          );
        })}
      </div>
    </>
  );
}
