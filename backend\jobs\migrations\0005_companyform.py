# Generated by Django 5.1.5 on 2025-06-21 18:54

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('jobs', '0004_jobposting_on_campus'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyForm',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('company', models.CharField(max_length=255)),
                ('key', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('submitted', models.BooleanField(default=False)),
                ('details', models.J<PERSON><PERSON>ield(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
