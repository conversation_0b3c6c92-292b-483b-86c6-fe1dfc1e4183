import client from './client';

// List all jobs with pagination and filtering
export function listJobs(params = {}) {
  const queryParams = new URLSearchParams();
  
  // Add pagination parameters
  if (params.page) queryParams.append('page', params.page);
  if (params.per_page) queryParams.append('per_page', params.per_page);
  
  // Add filtering parameters
  if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);
  if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);
  if (params.salary_min) queryParams.append('salary_min', params.salary_min);
  if (params.search) queryParams.append('search', params.search);
  
  const queryString = queryParams.toString();
  const url = `/api/v1/college/default-college/jobs/${queryString ? `?${queryString}` : ''}`;
  
  return client.get(url);
}

// Apply to a job
export function applyToJob(job, coverLetter) {
  return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, {
    cover_letter: coverLetter,
    job: job,         // Including jobId in the body as per your observation
  });
}

// Get job details by ID
export function getJobById(jobId) {
  return client.get(`/api/v1/college/default-college/jobs/${jobId}/`);
}

// List jobs the current student has applied to
export function listAppliedJobs() {
  return client.get('/api/v1/college/default-college/jobs/applied/');
}

// Admin API functions for managing jobs and companies

// List all companies (for admin)
export function listCompanies() {
  return client.get('/api/v1/college/default-college/companies/');
}

// Get company details
export function getCompany(companyId) {
  return client.get(`/api/v1/college/default-college/companies/${companyId}/`);
}

// Create a new company
export function createCompany(companyData) {
  return client.post('/api/v1/college/default-college/companies/', companyData);
}

// Update company details
export function updateCompany(companyId, companyData) {
  return client.put(`/api/v1/college/default-college/companies/${companyId}/`, companyData);
}

// Delete a company
export function deleteCompany(companyId) {
  return client.delete(`/api/v1/college/default-college/companies/${companyId}/`);
}

// Create a new job posting
export function createJob(jobData) {
  return client.post('/api/v1/college/default-college/jobs/create/', jobData);
}

// Update job posting
export function updateJob(jobId, jobData) {
  return client.put(`/api/v1/college/default-college/jobs/${jobId}/`, jobData);
}

// Delete job posting
export function deleteJob(jobId) {
  return client.delete(`/api/v1/college/default-college/jobs/${jobId}/`);
}

// Get job applications for admin
export function getJobApplications(jobId) {
  return client.get(`/api/v1/college/default-college/jobs/${jobId}/applications/`);
}

// Get all applications for admin dashboard
export function getAllApplications() {
  return client.get('/api/v1/college/default-college/applications/');
}

// Admin-specific job listing (shows all jobs including unpublished)
export function listJobsAdmin(params = {}) {
  const queryParams = new URLSearchParams();
  
  // Add pagination parameters
  if (params.page) queryParams.append('page', params.page);
  if (params.per_page) queryParams.append('per_page', params.per_page);
  
  // Add filtering parameters
  if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);
  if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);
  if (params.salary_min) queryParams.append('salary_min', params.salary_min);
  if (params.search) queryParams.append('search', params.search);
  if (params.is_published !== undefined) queryParams.append('is_published', params.is_published);
  
  const queryString = queryParams.toString();
  const url = `/api/v1/college/default-college/jobs/admin/${queryString ? `?${queryString}` : ''}`;
  
  return client.get(url);
}

// Toggle job publish status
export function toggleJobPublish(jobId) {
  return client.patch(`/api/v1/jobs/${jobId}/toggle-publish/`);
}

