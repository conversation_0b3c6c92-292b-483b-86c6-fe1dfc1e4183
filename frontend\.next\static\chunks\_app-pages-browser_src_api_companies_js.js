"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_api_companies_js"],{

/***/ "(app-pages-browser)/./src/api/companies.js":
/*!******************************!*\
  !*** ./src/api/companies.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFollowingStatus: () => (/* binding */ checkFollowingStatus),\n/* harmony export */   createCompany: () => (/* binding */ createCompany),\n/* harmony export */   deleteCompany: () => (/* binding */ deleteCompany),\n/* harmony export */   fetchCompanies: () => (/* binding */ fetchCompanies),\n/* harmony export */   followCompany: () => (/* binding */ followCompany),\n/* harmony export */   getCompany: () => (/* binding */ getCompany),\n/* harmony export */   getCompanyStats: () => (/* binding */ getCompanyStats),\n/* harmony export */   getFollowersCount: () => (/* binding */ getFollowersCount),\n/* harmony export */   getIndustries: () => (/* binding */ getIndustries),\n/* harmony export */   getUserFollowedCompanies: () => (/* binding */ getUserFollowedCompanies),\n/* harmony export */   listCompanies: () => (/* binding */ listCompanies),\n/* harmony export */   transformCompanyData: () => (/* binding */ transformCompanyData),\n/* harmony export */   unfollowCompany: () => (/* binding */ unfollowCompany),\n/* harmony export */   updateCompany: () => (/* binding */ updateCompany),\n/* harmony export */   uploadCompanyLogo: () => (/* binding */ uploadCompanyLogo)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/api/client.js\");\n\n// List all companies with optional filtering\nfunction listCompanies() {\n    let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const queryParams = new URLSearchParams();\n    // Add pagination parameters\n    if (params.page) queryParams.append('page', params.page);\n    if (params.per_page) queryParams.append('per_page', params.per_page);\n    // Add filtering parameters\n    if (params.tier && params.tier !== 'ALL') queryParams.append('tier', params.tier);\n    if (params.industry && params.industry !== 'ALL') queryParams.append('industry', params.industry);\n    if (params.campus_recruiting) queryParams.append('campus_recruiting', params.campus_recruiting);\n    if (params.search) queryParams.append('search', params.search);\n    if (params.sort) queryParams.append('sort', params.sort);\n    // Add cache busting parameter to prevent cached responses\n    queryParams.append('_t', new Date().getTime());\n    const queryString = queryParams.toString();\n    // Try both endpoints to maximize compatibility with backend\n    const urls = [\n        \"/api/v1/companies/\".concat(queryString ? \"?\".concat(queryString) : ''),\n        \"/api/v1/college/default-college/companies/\".concat(queryString ? \"?\".concat(queryString) : '')\n    ];\n    // Try primary endpoint first, fall back to secondary if needed\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[0]).catch((error)=>{\n        console.log(\"Primary endpoint failed: \".concat(error.message, \", trying fallback...\"));\n        return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[1]);\n    });\n}\n// Function to fetch companies from the API with improved reliability\nasync function fetchCompanies() {\n    let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        console.log('Fetching companies from API...');\n        const response = await listCompanies(params);\n        // Transform the data to match our frontend structure\n        let companies = [];\n        if (response.data && Array.isArray(response.data)) {\n            companies = response.data;\n        } else if (response.data && response.data.results && Array.isArray(response.data.results)) {\n            companies = response.data.results;\n        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n            companies = response.data.data;\n        }\n        console.log(\"Retrieved \".concat(companies.length, \" companies from API\"));\n        // Only proceed with detail fetching if we got a reasonable number of companies\n        if (companies.length > 0) {\n            // For each company in the list, fetch complete details to get all fields\n            const detailedCompanies = await Promise.all(companies.map(async (company)=>{\n                try {\n                    // Fetch detailed info for each company\n                    const detailResponse = await getCompany(company.id);\n                    return transformCompanyData(detailResponse.data);\n                } catch (error) {\n                    console.log(\"Could not fetch details for company \".concat(company.id, \":\"), error);\n                    // Fall back to the list data if detail fetch fails\n                    return transformCompanyData(company);\n                }\n            }));\n            // Store companies in sessionStorage for quick access\n            if (true) {\n                sessionStorage.setItem('companies_data', JSON.stringify(detailedCompanies));\n                sessionStorage.setItem('companies_timestamp', Date.now());\n            }\n            return detailedCompanies;\n        } else {\n            throw new Error('No companies returned from API');\n        }\n    } catch (error) {\n        console.error('Error fetching companies from API:', error);\n        // Check if we have cached data in sessionStorage\n        if (true) {\n            const cachedData = sessionStorage.getItem('companies_data');\n            const timestamp = sessionStorage.getItem('companies_timestamp');\n            if (cachedData && timestamp) {\n                // Only use cached data if it's less than 5 minutes old\n                const age = Date.now() - parseInt(timestamp);\n                if (age < 5 * 60 * 1000) {\n                    console.log('Using cached company data (< 5 min old)');\n                    return JSON.parse(cachedData);\n                }\n            }\n        }\n        // Import the static data as last resort\n        console.log('Falling back to static company data');\n        const { companies } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_data_jobsData_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../data/jobsData */ \"(app-pages-browser)/./src/data/jobsData.js\"));\n        return companies;\n    }\n}\n// Get a single company by ID with better error handling\nfunction getCompany(companyId) {\n    // Try both possible endpoints\n    const urls = [\n        \"/api/v1/company/\".concat(companyId, \"/\"),\n        \"/api/v1/companies/\".concat(companyId, \"/\"),\n        \"/api/v1/college/default-college/companies/\".concat(companyId, \"/\")\n    ];\n    // Try each URL in sequence until one works\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[0]).catch((error1)=>{\n        console.log(\"First company endpoint failed: \".concat(error1.message, \", trying second...\"));\n        return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[1]).catch((error2)=>{\n            console.log(\"Second company endpoint failed: \".concat(error2.message, \", trying third...\"));\n            return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(urls[2]);\n        });\n    });\n}\n// Create a new company\nfunction createCompany(companyData) {\n    const formData = new FormData();\n    // Append all fields to the FormData\n    Object.keys(companyData).forEach((key)=>{\n        // Handle file upload for logo\n        if (key === 'logo' && companyData[key] instanceof File) {\n            formData.append(key, companyData[key]);\n        } else if (companyData[key] !== null && companyData[key] !== undefined) {\n            formData.append(key, companyData[key]);\n        }\n    });\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/v1/companies/', formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n}\n// Update company details\nfunction updateCompany(companyId, companyData) {\n    const formData = new FormData();\n    // Append all fields to the FormData\n    Object.keys(companyData).forEach((key)=>{\n        // Handle file upload for logo\n        if (key === 'logo' && companyData[key] instanceof File) {\n            formData.append(key, companyData[key]);\n        } else if (companyData[key] !== null && companyData[key] !== undefined) {\n            formData.append(key, companyData[key]);\n        }\n    });\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/api/v1/companies/\".concat(companyId, \"/\"), formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n}\n// Delete a company\nfunction deleteCompany(companyId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/api/v1/companies/\".concat(companyId, \"/\"));\n}\n// Upload company logo\nfunction uploadCompanyLogo(companyId, logoFile) {\n    const formData = new FormData();\n    formData.append('logo', logoFile);\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/companies/\".concat(companyId, \"/upload-logo/\"), formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n}\n// Get company statistics\nfunction getCompanyStats() {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/companies/stats/');\n}\n// Get unique industries\nfunction getIndustries() {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/v1/companies/industries/');\n}\n// Transform backend company data to match frontend structure\nfunction transformCompanyData(backendData) {\n    return {\n        id: backendData.id,\n        name: backendData.name,\n        logo: backendData.logo || \"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=\".concat(backendData.name.charAt(0)),\n        description: backendData.description || '',\n        industry: backendData.industry || '',\n        size: backendData.size || 'Size not specified',\n        founded: backendData.founded || '',\n        location: backendData.location || 'Location not specified',\n        website: backendData.website || '',\n        tier: backendData.tier || 'Tier 3',\n        campus_recruiting: backendData.campus_recruiting || false,\n        totalActiveJobs: backendData.total_active_jobs || 0,\n        totalApplicants: backendData.total_applicants || 0,\n        totalHired: backendData.total_hired || 0,\n        awaitedApproval: backendData.awaited_approval || 0\n    };\n}\n// Get followers count for a company\nfunction getFollowersCount(companyId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/companies/\".concat(companyId, \"/followers/count/\"));\n}\n// Follow a company\nfunction followCompany(companyId, userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/companies/\".concat(companyId, \"/followers/\"), {\n        user_id: userId\n    });\n}\n// Unfollow a company\nfunction unfollowCompany(companyId, userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/api/v1/companies/\".concat(companyId, \"/followers/\"), {\n        data: {\n            user_id: userId\n        }\n    });\n}\n// Check if user is following a company\nfunction checkFollowingStatus(companyId, userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/companies/\".concat(companyId, \"/followers/status/?user_id=\").concat(userId));\n}\n// Get all companies a user is following\nfunction getUserFollowedCompanies(userId) {\n    return _client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/users/\".concat(userId, \"/following/\"));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/api/companies.js\n"));

/***/ })

}]);