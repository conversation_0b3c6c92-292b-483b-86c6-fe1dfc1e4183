import { 
  IconHome, 
  IconBriefcase, 
  IconCompass, 
  IconBuilding, 
  IconUsers, 
  IconCalendarEvent, 
  IconMail 
} from '@tabler/icons-react';

// Shared navigation configuration for all student pages
export const navigationLinks = [
  {
    items: [
      { title: 'My Campus', href: '/', icon: <IconHome /> },
      { title: 'My Jobs', href: '/myjobs', icon: <IconBriefcase /> },
      { title: 'Explore', href: '/explore', icon: <IconCompass /> },
      { title: 'Inbox', href: '/inbox', icon: <IconMail /> }
    ]
  },
  {
    items: [
      { title: 'Job Postings', href: '/jobpostings', icon: <IconBuilding /> },
      { title: 'Employers', href: '/employers', icon: <IconUsers /> },
      { title: 'Events', href: '/events', icon: <IconCalendarEvent /> },
      { title: 'Calendar', href: '/calendar', icon: <IconCalendarEvent /> }
    ]
  }
]; 