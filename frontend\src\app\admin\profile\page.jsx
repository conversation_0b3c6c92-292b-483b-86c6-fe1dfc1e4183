'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { IconUser } from '@tabler/icons-react';
import { uploadResume } from '../../../api/auth';
import { studentsAPI } from '../../../api/students';

function DropdownMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const router = useRouter();

  const menuItems = [
    { title: 'My Profile', href: '/profile' },
    { title: 'Notification Preferences', href: '#' },
    { title: 'Settings', href: '#' },
    { title: 'Help Center', href: '#' },
    { title: 'Terms of Service', href: '#' }
  ];

  const handleLogout = () => {
    localStorage.removeItem('userEmail');
    localStorage.removeItem('collegeName');
    localStorage.removeItem('role');
    document.cookie = 'role=; path=/; max-age=0';
    router.push('/login');
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative flex items-center gap-3" ref={dropdownRef}>
      <span className="text-black font-medium">Student Career Center</span>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-blue-600 hover:text-blue-700 p-2 rounded-full hover:bg-blue-50 transition-colors"
      >
        <IconUser size={24} />
      </button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {menuItems.map((item, index) => (
            <a
              key={index}
              href={item.href}
              className="block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transform hover:scale-105 transition-all duration-200"
            >
              {item.title}
            </a>
          ))}
          <button
            onClick={handleLogout}
            className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 transform hover:scale-105 transition-all duration-200"
          >
            Logout
          </button>
        </div>
      )}
    </div>
  );
}

function PhotoBox() {
  const [image, setImage] = useState('/images/default-avatar.png');
  const [isEditing, setIsEditing] = useState(false);
  const fileInputRef = useRef(null);

  const handleEditClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImage(e.target.result);
        setIsEditing(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePhotoSave = () => { 
    setIsEditing(false);
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col items-center bg-white rounded-2xl p-6 shadow-sm">
        <img 
          src={image} 
          alt="Profile" 
          className="w-32 h-48 object-cover border-2 border-gray-100 rounded-lg"
        />
        <div className="flex gap-2 mt-2">
          <button
            onClick={handleEditClick}
            className="text-sm px-3 py-1 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
          >
            Edit
          </button>
          {isEditing && (
            <button
              onClick={handlePhotoSave} 
              className="text-sm px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          className="hidden"
        />
      </div>
    </div>
  );
}

function StudentProfilePage() {
  const [selectedItem, setSelectedItem] = useState('Personal Information');
  const menuItems = [
    'Personal Information',
    'Documents',
    'Academics'
  ];

  // Add this state at the beginning of your component
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    address: '',
    linkedin: '',
    fullName: '',
    preferredName: '',
    dateOfBirth: '',
    pronouns: '',
    profileVisibility: 'public',
    resumeVisibility: 'public',
    gender: '',
    nationality: '',
    category: '',
    disabilityStatus: ''
  });

  // Add loading and error state
  const [loading, setLoading] = useState(true);
  const [profileError, setProfileError] = useState('');

  // Fetch user profile on mount
  useEffect(() => {
    async function fetchProfile() {
      setLoading(true);
      setProfileError('');
      try {
        const profile = await studentsAPI.getProfile();
        // Handle both student and admin user types
        let isStudent = profile.user_type === 'STUDENT' || profile.user?.user_type === 'STUDENT';
        if (isStudent) {
          setFormData({
            email: profile.user?.email || profile.email || '',
            phone: profile.phone || profile.contact_email || '',
            address: profile.address || '',
            linkedin: profile.linkedin || '',
            fullName: [profile.first_name, profile.last_name].filter(Boolean).join(' '),
            preferredName: profile.preferred_name || '',
            dateOfBirth: profile.date_of_birth || '',
            pronouns: profile.pronouns || '',
            profileVisibility: profile.profile_visibility || 'public',
            resumeVisibility: profile.resume_visibility || 'public',
            gender: profile.gender || '',
            nationality: profile.nationality || '',
            category: profile.category || '',
            disabilityStatus: profile.disability_status || ''
          });
        } else {
          // For admin or other user types, fallback to minimal info
          setFormData({
            email: profile.email || '',
            phone: '',
            address: '',
            linkedin: '',
            fullName: [profile.first_name, profile.last_name].filter(Boolean).join(' '),
            preferredName: '',
            dateOfBirth: '',
            pronouns: '',
            profileVisibility: 'public',
            resumeVisibility: 'public',
            gender: '',
            nationality: '',
            category: '',
            disabilityStatus: ''
          });
        }
      } catch (err) {
        setProfileError('Failed to load profile data.');
      } finally {
        setLoading(false);
      }
    }
    fetchProfile();
  }, []);

  const [achievements, setAchievements] = useState([]);
  const [editingAchievement, setEditingAchievement] = useState(null);
  const handleAchievementEdit = (achievement) => {
    setEditingAchievement({ ...achievement });
    setIsEditing(true);
  };
  
  const handleAchievementChange = (field, value) => {
    setEditingAchievement(prev => ({
      ...prev,
      [field]: value
    }));
  };
  const handleCertificateUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      setEditingAchievement(prev => ({
        ...prev,
        certificate: file
      }));
    }
  };
  const handleAchievementSave = () => {
    if (editingAchievement.id) {
      setAchievements(prev =>
        prev.map(ach =>
          ach.id === editingAchievement.id ? editingAchievement : ach
        )
      );
    } else {
      const newAchievement = {
        id: achievements.length + 1,
        ...editingAchievement
      };
      setAchievements(prev => [...prev, newAchievement]);
    }
    setIsEditing(false);
    setEditingAchievement(null);
  };
  
  const [academics, setAcademics] = useState([
    {
      id: 1,
      degree: '',
      branch: '',
      institution: '',
      university: '',
      startYear: '',
      endYear: '',
      cgpa: ''
    }
  ]);
  const [editingAcademic, setEditingAcademic] = useState(null);
  const handleAcademicEdit = (academic) => {
    setEditingAcademic({ ...academic });
    setIsEditing(true);
  };
  
  const handleAcademicChange = (field, value) => {
    setEditingAcademic(prev => ({
      ...prev,
      [field]: value
    }));
  };
  const handleAcademicSave = () => {
    if (editingAcademic.id) {
      setAcademics(prev =>
        prev.map(acad =>
          acad.id === editingAcademic.id ? editingAcademic : acad
        )
      );
    } else {
      const newAcademic = {
        id: academics.length + 1,
        ...editingAcademic
      };
      setAcademics(prev => [...prev, newAcademic]);
    }
    setIsEditing(false);
    setEditingAcademic(null);
  };
  const [isEditing, setIsEditing] = useState(false);
  const [workExperiences, setWorkExperiences] = useState([
    {
      id: 1,
      company: 'Tech Solutions Inc.',
      position: 'Software Developer Intern',
      duration: 'May 2023 - Aug 2023',
      description: 'Developed and maintained web applications using React.js and Node.js. Collaborated with senior developers on various projects.'
    },
    {
      id: 2,
      company: 'Digital Innovations Ltd.',
      position: 'Frontend Developer',
      duration: 'Sep 2023 - Present',
      description: 'Leading frontend development for multiple client projects. Implementing responsive designs and optimizing application performance.'
    }
  ]);
  const handleAddExperience = () => {
    setIsEditing(true);
    setEditingExperience({
      company: '',
      position: '',
      duration: '',
      description: ''
    });
  };
  const [editingExperience, setEditingExperience] = useState({
    company: '',
    position: '',
    duration: '',
    description: ''
  });
  const handleExperienceChange = (field, value) => {
    setEditingExperience(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleExperienceEdit = (experience) => {
    setIsEditing(true);
    setEditingExperience({
      id: experience.id,
      company: experience.company,
      position: experience.position,
      duration: experience.duration,
      description: experience.description
    });
  };

  const handleExperienceSave = () => {
    if (editingExperience.id) {
      // Update existing experience
      setWorkExperiences(prev =>
        prev.map(exp =>
          exp.id === editingExperience.id ? editingExperience : exp
        )
      );
    } else {
      // Add new experience
      const newExperience = {
        id: workExperiences.length + 1,
        ...editingExperience
      };
      setWorkExperiences(prev => [...prev, newExperience]);
    }
    setIsEditing(false);
    setEditingExperience({
      company: '',
      position: '',
      duration: '',
      description: ''
    });
  };
  const [projects, setProjects] = useState([
    {
      id: 1,
      name: "Expense Tracker",
      description: "A web application to track personal expenses",
      details: [
        "Built with React and Node.js",
        "Implements user authentication",
        "Features expense categorization"
      ],
      date: "2023-01-15"
    },
    {
      id: 2,
      name: "CPU Scheduler",
      description: "A simulation of CPU scheduling algorithms",
      details: [
        "Implements various scheduling algorithms",
        "Visual representation of process execution",
        "Performance metrics calculation"
      ],
      date: "2022-11-20"
    }
  ]);

  const [editingProject, setEditingProject] = useState(null);
  const [documents, setDocuments] = useState({
    resume: null,
    tenthCertificate: null,
    twelfthCertificate: null,
    semesterMarksheets: []
  });

  const handleProjectEdit = (project) => {
    setEditingProject({ ...project });
    setIsEditing(true);
  };

  const handleEdit1 = () => {
    if (selectedItem === 'Projects') {
      setEditingProject({
        id: Date.now(),
        name: '',
        description: '',
        details: [''],
        date: new Date().toISOString().split('T')[0]
      });
    }
    setIsEditing(true);
  };

  const handleSave1 = () => {
    if (selectedItem === 'Projects' && editingProject) {
      const projectExists = projects.some(p => p.id === editingProject.id);
      if (projectExists) {
        setProjects(projects.map(p => p.id === editingProject.id ? editingProject : p));
      } else {
        setProjects([...projects, editingProject]);
      }
      setEditingProject(null);
    }
    setIsEditing(false);
  };
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProjectChange = (field, value) => {
    setEditingProject(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleProjectDetailChange = (index, value) => {
    const newDetails = [...editingProject.details];
    newDetails[index] = value;
    setEditingProject(prev => ({
      ...prev,
      details: newDetails
    }));
  };

const handleDocumentUpload = (event, type) => {
  const file = event.target.files[0];

  if (file) {
    if (type === 'resume') {
      // Upload the resume to the server using the API
      const accessToken = localStorage.getItem('access'); // Get the access token from localStorage
      uploadResume(file, accessToken)
        .then(response => {
          console.log('Resume uploaded successfully:', response.data.message);
          setDocuments(prev => ({ ...prev, resume: file })); // Update the state with the uploaded file
        })
        .catch(error => {
          console.error('Error uploading resume:', error.response?.data?.message || error.message);
        });
    } else {
      // Handle other document uploads
      setDocuments(prev => ({
        ...prev,
        [type]: file
      }));
    }
  }
};

  const handleDocumentSave = () => {
    setIsEditing(false);
  };

  const handlePersonalInfoEdit = () => {
    setIsEditing(true);
  };

  const handlePersonalInfoSave = () => {
    setIsEditing(false);
  };

  if (selectedItem === 'Personal Information') {
    return (
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-medium text-black">Personal Information</h3>
          <div>
            {!isEditing ? (
              <button
                onClick={handlePersonalInfoEdit}
                className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
              >
                Edit
              </button>
            ) : (
              <button
                onClick={handlePersonalInfoSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save
              </button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="text-gray-500">Loading...</div>
        ) : profileError ? (
          <div className="text-red-500">{profileError}</div>
        ) : (
        <div className="space-y-4 overflow-auto max-h-[calc(100vh-200px)]">
          <div className="space-y-4">
            <h4 className="text-lg text-black">Contact Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 text-black"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">LinkedIn</label>
                <input
                  type="url"
                  name="linkedin"
                  value={formData.linkedin}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg text-black">Basic Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Preferred Name</label>
                <input
                  type="text"
                  name="preferredName"
                  value={formData.preferredName}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Pronouns</label>
                <input
                  type="text"
                  name="pronouns"
                  value={formData.pronouns}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg text-black">Privacy Settings</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Profile Visibility</label>
                <select
                  name="profileVisibility"
                  value={formData.profileVisibility}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                >
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                  <option value="connections">Connections Only</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Resume Visibility</label>
                <select
                  name="resumeVisibility"
                  value={formData.resumeVisibility}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                >
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                  <option value="connections">Connections Only</option>
                </select>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg text-black">Additional Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                <select
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                >
                  <option value="">Select</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                  <option value="Prefer not to say">Prefer not to say</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nationality</label>
                <input
                  type="text"
                  name="nationality"
                  value={formData.nationality}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                >
                  <option value="">Select</option>
                  <option value="General">General</option>
                  <option value="OBC">OBC</option>
                  <option value="SC">SC</option>
                  <option value="ST">ST</option>
                  <option value="EWS">EWS</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Disability Status</label>
                <select
                  name="disabilityStatus"
                  value={formData.disabilityStatus}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                >
                  <option value="">Select</option>
                  <option value="None">None</option>
                  <option value="Yes">Yes</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        )}
      </div>
    );
  }

  if (selectedItem === 'Documents') {
    return (
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold text-black">Documents</h3>
          <div>
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
              >
                Edit
              </button>
            ) : (
              <button
                onClick={handleDocumentSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save
              </button>
            )}
          </div>
        </div>
        <div className="space-y-6 overflow-auto max-h-[calc(100vh-200px)]">
          <div className="space-y-2">
            <label className="block text-black font-medium">Resume</label>
            <div className="flex items-center gap-4">
              <span className="text-gray-600">
                {documents.resume ? documents.resume.name : 'No file selected'}
              </span>
              {isEditing && (
                <>
                  <input
                    type="file"
                    onChange={(e) => handleDocumentUpload(e, 'resume')}
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    id="resume-upload"
                  />
                  <label
                    htmlFor="resume-upload"
                    className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer"
                  >
                    Upload
                  </label>
                </>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-black font-medium">10th Certificate</label>
            <div className="flex items-center gap-4">
              <span className="text-gray-600">
                {documents.tenthCertificate ? documents.tenthCertificate.name : 'No file selected'}
              </span>
              {isEditing && (
                <>
                  <input
                    type="file"
                    onChange={(e) => handleDocumentUpload(e, 'tenthCertificate')}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="hidden"
                    id="tenth-cert-upload"
                  />
                  <label
                    htmlFor="tenth-cert-upload"
                    className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer"
                  >
                    Upload
                  </label>
                </>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-black font-medium">12th Certificate</label>
            <div className="flex items-center gap-4">
              <span className="text-gray-600">
                {documents.twelfthCertificate ? documents.twelfthCertificate.name : 'No file selected'}
              </span>
              {isEditing && (
                <>
                  <input
                    type="file"
                    onChange={(e) => handleDocumentUpload(e, 'twelfthCertificate')}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="hidden"
                    id="twelfth-cert-upload"
                  />
                  <label
                    htmlFor="twelfth-cert-upload"
                    className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer"
                  >
                    Upload
                  </label>
                </>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-black font-medium">Semester Marksheets</label>
            <div className="space-y-4">
              {documents.semesterMarksheets.map((file, index) => (
                <div key={index} className="flex items-center gap-4">
                  <span className="text-gray-600">{file.name}</span>
                </div>
              ))}
              {isEditing && (
                <div className="flex items-center gap-4">
                  <input
                    type="file"
                    onChange={(e) => handleDocumentUpload(e, 'semesterMarksheets')}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="hidden"
                    id="semester-marksheet-upload"
                  />
                  <label
                    htmlFor="semester-marksheet-upload"
                    className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer"
                  >
                    Add Marksheet
                  </label>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (selectedItem === 'Projects') {
    return (
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-medium text-black">Projects</h3>
          <div>
            {!isEditing ? (
              <button
                onClick={handleEdit1}
                className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
              >
                Add
              </button>
            ) : (
              <button
                onClick={handleSave1}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save
              </button>
            )}
          </div>
        </div>

        {isEditing && editingProject ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-black font-medium">Project Name</label>
              <input
                type="text"
                value={editingProject.name}
                onChange={(e) => handleProjectChange('name', e.target.value)}
                className="w-full p-2 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none bg-gray-50 text-black"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-black font-medium">Description</label>
              <input
                type="text"
                value={editingProject.description}
                onChange={(e) => handleProjectChange('description', e.target.value)}
                className="w-full p-2 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none bg-gray-50 text-black"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-black font-medium">Details</label>
              {editingProject.details.map((detail, index) => (
                <div key={index} className="flex gap-2 items-center">
                  <input
                    type="text"
                    value={detail}
                    onChange={(e) => handleProjectDetailChange(index, e.target.value)}
                    className="w-full p-2 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none bg-gray-50 text-black"
                  />
                </div>
              ))}
              <button
                onClick={() => handleProjectChange('details', [...editingProject.details, ''])}
                className="mt-2 text-sm px-3 py-1 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
              >
                Add Detail
              </button>
            </div>
            <div className="space-y-2">
              <label className="block text-black font-medium">Date</label>
              <input
                type="date"
                value={editingProject.date}
                onChange={(e) => handleProjectChange('date', e.target.value)}
                className="w-full p-2 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none bg-gray-50 text-black"
              />
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <button
                onClick={() => {
                  setIsEditing(false);
                  setEditingProject(null);
                }}
                className="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-6 overflow-auto max-h-[calc(100vh-200px)]">
            {projects.map((project) => (
              <div key={project.id} className="border-b border-gray-200 pb-6 last:border-0">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-lg font-medium text-black">{project.name}</h4>
                    <p className="text-gray-600">{project.description}</p>
                  </div>
                  <button
                    onClick={() => handleProjectEdit(project)}
                    className="text-sm px-3 py-1 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    Edit
                  </button>
                </div>
                <ul className="mt-2 space-y-1 list-disc list-inside text-gray-600">
                  {project.details.map((detail, index) => (
                    <li key={index}>{detail}</li>
                  ))}
                </ul>
                <p className="mt-2 text-sm text-gray-500">{project.date}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  if (selectedItem === 'Work History') {
    return (
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold text-black">Work History</h3>
          {!isEditing ? (
            <button
              onClick={handleAddExperience}
              className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
            >
              Add Experience
            </button>
          ) : (
            <button
              onClick={handleExperienceSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
  
        <div className="space-y-4">
          {isEditing ? (
            // Edit form
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="block text-black font-medium">Company Name</label>
                <input
                  type="text"
                  value={editingExperience.company}
                  onChange={(e) => handleExperienceChange('company', e.target.value)}
                  className="w-full p-2 rounded-lg border focus:ring-2 focus:ring-blue-500 outline-none text-black"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-black font-medium">Position</label>
                <input
                  type="text"
                  value={editingExperience.position}
                  onChange={(e) => handleExperienceChange('position', e.target.value)}
                  className="w-full p-2 rounded-lg border focus:ring-2 focus:ring-blue-500 outline-none text-black"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-black font-medium">Duration</label>
                <input
                  type="text"
                  value={editingExperience.duration}
                  onChange={(e) => handleExperienceChange('duration', e.target.value)}
                  className="w-full p-2 rounded-lg border focus:ring-2 focus:ring-blue-500 outline-none text-black"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-black font-medium">Description</label>
                <textarea
                  value={editingExperience.description}
                  onChange={(e) => handleExperienceChange('description', e.target.value)}
                  className="w-full p-2 rounded-lg border focus:ring-2 focus:ring-blue-500 outline-none h-32 text-black"
                />
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {workExperiences.map((experience) => (
                <div key={experience.id} className="border rounded-lg p-4 space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-lg font-medium text-black">{experience.company}</h4>
                      <p className="text-blue-600">{experience.position}</p>
                      <p className="text-gray-600">{experience.duration}</p>
                    </div>
                    <button
                      onClick={() => handleExperienceEdit(experience)}
                      className="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors"
                    >
                      Edit
                    </button>
                  </div>
                  <p className="text-gray-700">{experience.description}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }
  if (selectedItem === 'Academics') {
    return (
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-medium text-black">Academic Details</h3>
        
        </div>
  
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Degree</label>
              <input
                type="text"
                value={editingAcademic.degree}
                onChange={(e) => handleAcademicChange('degree', e.target.value)}
                placeholder="e.g., B.Tech, M.Tech"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Branch / Stream</label>
              <input
                type="text"
                value={editingAcademic.branch}
                onChange={(e) => handleAcademicChange('branch', e.target.value)}
                placeholder="e.g., Computer Science"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Institution Name</label>
              <input
                type="text"
                value={editingAcademic.institution}
                onChange={(e) => handleAcademicChange('institution', e.target.value)}
                placeholder="Enter institution name"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">University / Board</label>
              <input
                type="text"
                value={editingAcademic.university}
                onChange={(e) => handleAcademicChange('university', e.target.value)}
                placeholder="Enter university/board name"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Start Year</label>
                <input
                  type="number"
                  value={editingAcademic.startYear}
                  onChange={(e) => handleAcademicChange('startYear', e.target.value)}
                  placeholder="YYYY"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">End Year</label>
                <input
                  type="number"
                  value={editingAcademic.endYear}
                  onChange={(e) => handleAcademicChange('endYear', e.target.value)}
                  placeholder="YYYY"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">CGPA / Percentage</label>
              <input
                type="text"
                value={editingAcademic.cgpa}
                onChange={(e) => handleAcademicChange('cgpa', e.target.value)}
                placeholder="Enter CGPA or Percentage"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {academics.map((academic) => (
              <div
                key={academic.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="text-lg font-medium text-black">{academic.degree}</h4>
                    <p className="text-gray-600">{academic.branch}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div>
                    <p className="text-sm text-gray-500">Institution</p>
                    <p className="text-gray-700">{academic.institution}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">University/Board</p>
                    <p className="text-gray-700">{academic.university}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Duration</p>
                    <p className="text-gray-700">{academic.startYear} - {academic.endYear}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">CGPA/Percentage</p>
                    <p className="text-gray-700">{academic.cgpa}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }
  if (selectedItem === 'Achievements') {
    return (
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-medium text-black">Achievements</h3>
          <div>
            {!isEditing ? (
              <button
                onClick={() => {
                  setEditingAchievement({
                    id: null,
                    eventName: '',
                    description: '',
                    position: 'Others',
                    venue: '',
                    date: '',
                    certificate: null
                  });
                  setIsEditing(true);
                }}
                className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
              >
                Add Achievement
              </button>
            ) : (
              <button
                onClick={handleAchievementSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save
              </button>
            )}
          </div>
        </div>
  
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name of the Event</label>
              <input
                type="text"
                value={editingAchievement.eventName}
                onChange={(e) => handleAchievementChange('eventName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={editingAchievement.description}
                onChange={(e) => handleAchievementChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows="3"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
              <select
                value={editingAchievement.position}
                onChange={(e) => handleAchievementChange('position', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Winners">Winners</option>
                <option value="Runner ups">Runner ups</option>
                <option value="2nd Runner ups">2nd Runner ups</option>
                <option value="Finalists">Finalists</option>
                <option value="Others">Others</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Venue/Organizers</label>
              <input
                type="text"
                value={editingAchievement.venue}
                onChange={(e) => handleAchievementChange('venue', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date/Timeline of the Event</label>
              <input
                type="date"
                value={editingAchievement.date}
                onChange={(e) => handleAchievementChange('date', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Certificate (PDF only)</label>
              <input
                type="file"
                accept=".pdf"
                onChange={handleCertificateUpload}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {achievements.map((achievement) => (
              <div
                key={achievement.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="text-lg font-medium text-black">{achievement.eventName}</h4>
                    <p className="text-blue-600">{achievement.position}</p>
                  </div>
                  <button
                    onClick={() => handleAchievementEdit(achievement)}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    Edit
                  </button>
                </div>
                <p className="text-gray-600 mb-2">{achievement.description}</p>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Venue/Organizers</p>
                    <p className="text-gray-700">{achievement.venue}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Date</p>
                    <p className="text-gray-700">{achievement.date}</p>
                  </div>
                </div>
                {achievement.certificate && (
                  <div className="mt-2">
                  <a
                    href={URL.createObjectURL(new Blob([achievement.certificate], { type: 'application/pdf' }))}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    View Certificate
                  </a>
                </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }
  
  
  // Add a proper return for other sections
  return (
    <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
      {/* Add content for other sections */}
    </div>
  );
}

export { DropdownMenu };
export default StudentProfilePage;