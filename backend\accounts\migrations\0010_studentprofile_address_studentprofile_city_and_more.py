# Generated by Django 5.1.5 on 2025-07-01 14:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0009_studentprofile_joining_year_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='studentprofile',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='college_name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='country',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='district',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='gender',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='parent_contact',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='pincode',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='profile_image',
            field=models.ImageField(blank=True, null=True, upload_to='profile_images/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='tenth_board',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='tenth_certificate',
            field=models.FileField(blank=True, null=True, upload_to='certificates/10th/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='tenth_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='tenth_percentage',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='tenth_school',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='tenth_year_of_passing',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_board',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_certificate',
            field=models.FileField(blank=True, null=True, upload_to='certificates/12th/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_percentage',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_school',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_specialization',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='twelfth_year_of_passing',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.CreateModel(
            name='SemesterMarksheet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('semester', models.PositiveIntegerField()),
                ('cgpa', models.CharField(blank=True, max_length=10, null=True)),
                ('marksheet_file', models.FileField(blank=True, null=True, upload_to='marksheets/')),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semester_marksheets', to='accounts.studentprofile')),
            ],
            options={
                'ordering': ['semester'],
            },
        ),
    ]
