// Calendar data source for deadlines, interviews, and company events
// This file will serve as the single source of truth for all calendar-related data

import { studentApplications, jobPostings, companies } from './jobsData';

// Calendar event types
export const EVENT_TYPES = {
  APPLICATION_DEADLINE: 'APPLICATION_DEADLINE',
  INTERVIEW: 'INTERVIEW',
  COMPANY_EVENT: 'COMPANY_EVENT',
  CAREER_FAIR: 'CAREER_FAIR',
  DEADLINE_REMINDER: 'DEADLINE_REMINDER',
  OFFER_RESPONSE: 'OFFER_RESPONSE'
};

// Calendar events data
export const calendarEvents = [
  // Application Deadlines
  {
    id: 1,
    title: 'Google Software Engineering Intern - Application Deadline',
    type: EVENT_TYPES.APPLICATION_DEADLINE,
    date: '2024-03-15',
    time: '23:59',
    company: 'Google',
    jobId: 1,
    description: 'Final deadline to submit application for Google Software Engineering Internship',
    status: 'upcoming',
    priority: 'high',
    color: '#dc2626'
  },
  {
    id: 2,
    title: 'Microsoft Data Science Intern - Application Deadline',
    type: EVENT_TYPES.APPLICATION_DEADLINE,
    date: '2024-02-28',
    time: '23:59',
    company: 'Microsoft',
    jobId: 4,
    description: 'Last day to apply for Microsoft Data Science Internship position',
    status: 'upcoming',
    priority: 'high',
    color: '#dc2626'
  },
  {
    id: 3,
    title: 'Amazon Product Management Intern - Application Deadline',
    type: EVENT_TYPES.APPLICATION_DEADLINE,
    date: '2024-03-01',
    time: '23:59',
    company: 'Amazon',
    jobId: 6,
    description: 'Application deadline for Amazon Product Management Internship',
    status: 'upcoming',
    priority: 'high',
    color: '#dc2626'
  },

  // Interview Schedules
  {
    id: 4,
    title: 'AI Forge AR/VR Developer - Technical Interview',
    type: EVENT_TYPES.INTERVIEW,
    date: '2024-01-25',
    time: '14:00',
    company: 'AI Forge',
    jobId: 12,
    applicationId: 6,
    description: 'Technical interview focusing on Unity, 3D graphics, and AR/VR development',
    status: 'confirmed',
    priority: 'high',
    color: '#059669',
    location: 'Virtual - Google Meet',
    interviewer: 'Sarah Chen, Senior AR/VR Developer',
    duration: '60 minutes'
  },
  {
    id: 5,
    title: 'Cloudify Product Management - Final Round Interview',
    type: EVENT_TYPES.INTERVIEW,
    date: '2024-01-28',
    time: '10:30',
    company: 'Cloudify',
    jobId: 16,
    applicationId: 7,
    description: 'Final round interview with the product team and leadership',
    status: 'confirmed',
    priority: 'high',
    color: '#059669',
    location: 'On-site - Mumbai Office',
    interviewer: 'Raj Patel, Head of Product',
    duration: '90 minutes'
  },
  {
    id: 6,
    title: 'AI Forge UI/UX Designer - Portfolio Review',
    type: EVENT_TYPES.INTERVIEW,
    date: '2024-01-30',
    time: '15:00',
    company: 'AI Forge',
    jobId: 13,
    applicationId: 8,
    description: 'Portfolio review and design challenge discussion',
    status: 'scheduled',
    priority: 'medium',
    color: '#059669',
    location: 'Virtual - Zoom',
    interviewer: 'Alex Rodriguez, Design Lead',
    duration: '45 minutes'
  },

  // Company Events
  {
    id: 7,
    title: 'Google Campus Recruitment Info Session',
    type: EVENT_TYPES.COMPANY_EVENT,
    date: '2024-02-05',
    time: '18:00',
    company: 'Google',
    description: 'Learn about internship opportunities and company culture at Google',
    status: 'upcoming',
    priority: 'medium',
    color: '#2563eb',
    location: 'Main Auditorium',
    duration: '2 hours'
  },
  {
    id: 8,
    title: 'Microsoft Technical Workshop - Azure Cloud Computing',
    type: EVENT_TYPES.COMPANY_EVENT,
    date: '2024-02-10',
    time: '16:00',
    company: 'Microsoft',
    description: 'Hands-on workshop on Azure cloud services and development tools',
    status: 'upcoming',
    priority: 'medium',
    color: '#2563eb',
    location: 'Computer Lab 3',
    duration: '3 hours'
  },
  {
    id: 9,
    title: 'Amazon Leadership Principles Workshop',
    type: EVENT_TYPES.COMPANY_EVENT,
    date: '2024-02-15',
    time: '14:00',
    company: 'Amazon',
    description: 'Interactive session on Amazon\'s 16 Leadership Principles and behavioral interviews',
    status: 'upcoming',
    priority: 'medium',
    color: '#2563eb',
    location: 'Conference Hall A',
    duration: '2.5 hours'
  },

  // Career Fair
  {
    id: 10,
    title: 'Spring Career Fair 2024',
    type: EVENT_TYPES.CAREER_FAIR,
    date: '2024-03-20',
    time: '10:00',
    company: 'Multiple Companies',
    description: 'Annual spring career fair with 50+ top companies recruiting for internships and full-time positions',
    status: 'upcoming',
    priority: 'high',
    color: '#7c3aed',
    location: 'Sports Complex',
    duration: '6 hours'
  },

  // Deadline Reminders
  {
    id: 11,
    title: 'Reminder: Apple UX Design Intern - Application Due in 3 Days',
    type: EVENT_TYPES.DEADLINE_REMINDER,
    date: '2024-02-17',
    time: '09:00',
    company: 'Apple',
    jobId: 8,
    description: 'Reminder that Apple UX Design Internship application deadline is approaching',
    status: 'upcoming',
    priority: 'medium',
    color: '#f59e0b'
  },
  {
    id: 12,
    title: 'Reminder: Netflix Marketing Analytics - Application Due Tomorrow',
    type: EVENT_TYPES.DEADLINE_REMINDER,
    date: '2024-02-14',
    time: '09:00',
    company: 'Netflix',
    jobId: 9,
    description: 'Final reminder for Netflix Marketing Analytics Internship application',
    status: 'upcoming',
    priority: 'high',
    color: '#f59e0b'
  },

  // Offer Response Deadlines
  {
    id: 13,
    title: 'Respond to Cisco Cybersecurity Intern Offer',
    type: EVENT_TYPES.OFFER_RESPONSE,
    date: '2024-02-25',
    time: '17:00',
    company: 'Cisco',
    jobId: 10,
    description: 'Deadline to respond to the internship offer from Cisco',
    status: 'upcoming',
    priority: 'high',
    color: '#dc2626'
  }
];

// Additional upcoming events for the next few months
export const upcomingEvents = [
  {
    id: 14,
    title: 'Summer Internship Orientation',
    type: EVENT_TYPES.COMPANY_EVENT,
    date: '2024-05-15',
    time: '09:00',
    company: 'Multiple Companies',
    description: 'Orientation session for all summer 2024 interns',
    status: 'upcoming',
    priority: 'medium',
    color: '#2563eb',
    location: 'Career Services Center',
    duration: '4 hours'
  },
  {
    id: 15,
    title: 'Fall Recruitment Season Kickoff',
    type: EVENT_TYPES.COMPANY_EVENT,
    date: '2024-08-15',
    time: '10:00',
    company: 'Multiple Companies',
    description: 'Beginning of fall recruitment for full-time positions',
    status: 'upcoming',
    priority: 'medium',
    color: '#2563eb',
    location: 'Main Campus',
    duration: '1 day'
  }
];

// Helper functions
export const getEventsByDate = (date) => {
  return calendarEvents.filter(event => event.date === date);
};

export const getEventsByMonth = (year, month) => {
  const monthString = month.toString().padStart(2, '0');
  const yearMonth = `${year}-${monthString}`;
  return calendarEvents.filter(event => event.date.startsWith(yearMonth));
};

export const getEventsByType = (type) => {
  return calendarEvents.filter(event => event.type === type);
};

export const getEventsByCompany = (companyName) => {
  return calendarEvents.filter(event => 
    event.company.toLowerCase() === companyName.toLowerCase()
  );
};

export const getUpcomingEvents = (days = 7) => {
  const today = new Date();
  const futureDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));
  
  return calendarEvents.filter(event => {
    const eventDate = new Date(event.date);
    return eventDate >= today && eventDate <= futureDate;
  }).sort((a, b) => new Date(a.date) - new Date(b.date));
};

export const getEventsByPriority = (priority) => {
  return calendarEvents.filter(event => event.priority === priority);
};

export const getInterviewEvents = () => {
  return calendarEvents.filter(event => event.type === EVENT_TYPES.INTERVIEW);
};

export const getApplicationDeadlines = () => {
  return calendarEvents.filter(event => 
    event.type === EVENT_TYPES.APPLICATION_DEADLINE || 
    event.type === EVENT_TYPES.DEADLINE_REMINDER
  );
};

export const getEventStats = () => {
  const total = calendarEvents.length;
  const interviews = getEventsByType(EVENT_TYPES.INTERVIEW).length;
  const deadlines = getEventsByType(EVENT_TYPES.APPLICATION_DEADLINE).length;
  const companyEvents = getEventsByType(EVENT_TYPES.COMPANY_EVENT).length;
  const upcoming = getUpcomingEvents().length;

  return { total, interviews, deadlines, companyEvents, upcoming };
};

// Generate events from existing job applications
export const generateEventsFromApplications = () => {
  const generatedEvents = [];
  
  studentApplications.forEach(application => {
    // Add application deadline reminder
    const deadlineDate = new Date(application.application_deadline);
    const reminderDate = new Date(deadlineDate.getTime() - (3 * 24 * 60 * 60 * 1000)); // 3 days before
    
    if (reminderDate > new Date()) {
      generatedEvents.push({
        id: `reminder-${application.id}`,
        title: `Reminder: ${application.title} - Application Due Soon`,
        type: EVENT_TYPES.DEADLINE_REMINDER,
        date: reminderDate.toISOString().split('T')[0],
        time: '09:00',
        company: application.company,
        jobId: application.job_id,
        description: `Application deadline reminder for ${application.title} at ${application.company}`,
        status: 'upcoming',
        priority: 'medium',
        color: '#f59e0b'
      });
    }
    
    // Add interview events for applications with scheduled interviews
    if (application.status === 'INTERVIEW SCHEDULED') {
      const interviewDate = new Date();
      interviewDate.setDate(interviewDate.getDate() + Math.floor(Math.random() * 14) + 1); // Random future date
      
      generatedEvents.push({
        id: `interview-${application.id}`,
        title: `${application.company} ${application.title} - Interview`,
        type: EVENT_TYPES.INTERVIEW,
        date: interviewDate.toISOString().split('T')[0],
        time: '14:00',
        company: application.company,
        jobId: application.job_id,
        applicationId: application.id,
        description: `Interview for ${application.title} position at ${application.company}`,
        status: 'scheduled',
        priority: 'high',
        color: '#059669',
        location: 'Virtual - Teams Meeting',
        interviewer: 'HR Team',
        duration: '60 minutes'
      });
    }
  });
  
  return generatedEvents;
};

// All events including generated ones
export const getAllEvents = () => {
  return [...calendarEvents, ...generateEventsFromApplications()];
}; 