import { ArrowLeft, Calendar } from "lucide-react";

export default function PassoutYearCards({
  departmentLabel,
  onBack,
  getAvailablePassoutYears,
  allStudents,
  selectedDepartment,
  onSelectYear
}) {
  return (
    <>
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {departmentLabel} - Passout Years
            </h1>
            <p className="text-gray-600">Select a passout year to view students</p>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {getAvailablePassoutYears().map(year => {
          const count = allStudents.filter(s => {
            if (s.department !== selectedDepartment || !s.year || s.year === 'N/A') return false;
            const parts = s.year.split('-');
            return parts.length === 2 && parts[1] === String(year);
          }).length;
          return (
            <div
              key={year}
              onClick={() => onSelectYear(year)}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200 group"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-50 rounded-lg group-hover:bg-purple-100 transition-colors">
                  <Calendar className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{count}</div>
                  <div className="text-xs text-gray-500">Students</div>
                </div>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{year}</h3>
              <p className="text-sm text-gray-600">Passout Year</p>
            </div>
          );
        })}
      </div>
    </>
  );
}
