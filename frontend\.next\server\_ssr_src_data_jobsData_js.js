"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_data_jobsData_js";
exports.ids = ["_ssr_src_data_jobsData_js"];
exports.modules = {

/***/ "(ssr)/./src/data/jobsData.js":
/*!******************************!*\
  !*** ./src/data/jobsData.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   companies: () => (/* binding */ companies),\n/* harmony export */   fetchCompanies: () => (/* binding */ fetchCompanies),\n/* harmony export */   getActiveJobs: () => (/* binding */ getActiveJobs),\n/* harmony export */   getApplicationStats: () => (/* binding */ getApplicationStats),\n/* harmony export */   getCompaniesWithActiveJobs: () => (/* binding */ getCompaniesWithActiveJobs),\n/* harmony export */   getCompanyById: () => (/* binding */ getCompanyById),\n/* harmony export */   getJobById: () => (/* binding */ getJobById),\n/* harmony export */   getJobStats: () => (/* binding */ getJobStats),\n/* harmony export */   getJobsByCompany: () => (/* binding */ getJobsByCompany),\n/* harmony export */   jobPostings: () => (/* binding */ jobPostings),\n/* harmony export */   studentApplications: () => (/* binding */ studentApplications)\n/* harmony export */ });\n/* harmony import */ var _api_companies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api/companies */ \"(ssr)/./src/api/companies.js\");\n// Centralized data source for jobs, companies, and applications\n// This file will serve as the single source of truth for all job-related data\n\n// Initial static data for companies\nconst companies = [\n    {\n        id: 1,\n        name: \"Google\",\n        logo: \"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G\",\n        description: \"A multinational technology company specializing in internet-related services and products.\",\n        industry: \"Technology\",\n        size: \"100,000+ employees\",\n        founded: \"1998\",\n        location: \"Mountain View, CA\",\n        website: \"https://google.com\",\n        totalActiveJobs: 3,\n        totalApplicants: 1250,\n        totalHired: 89,\n        awaitedApproval: 2,\n        campus_recruiting: true,\n        tier: \"Tier 1\"\n    },\n    {\n        id: 2,\n        name: \"Microsoft\",\n        logo: \"https://via.placeholder.com/48x48/00A1F1/FFFFFF?text=M\",\n        description: \"A multinational technology corporation that develops computer software, consumer electronics, and personal computers.\",\n        industry: \"Technology\",\n        size: \"200,000+ employees\",\n        founded: \"1975\",\n        location: \"Seattle, WA\",\n        website: \"https://microsoft.com\",\n        totalActiveJobs: 2,\n        totalApplicants: 980,\n        totalHired: 67,\n        awaitedApproval: 1,\n        campus_recruiting: true,\n        tier: \"Tier 1\"\n    },\n    {\n        id: 3,\n        name: \"Amazon\",\n        logo: \"https://via.placeholder.com/48x48/FF9900/FFFFFF?text=A\",\n        description: \"A multinational technology company focusing on e-commerce, cloud computing, and artificial intelligence.\",\n        industry: \"E-commerce & Cloud\",\n        size: \"1,500,000+ employees\",\n        founded: \"1994\",\n        location: \"Seattle, WA\",\n        website: \"https://amazon.com\",\n        totalActiveJobs: 2,\n        totalApplicants: 1450,\n        totalHired: 112,\n        awaitedApproval: 3,\n        campus_recruiting: true,\n        tier: \"Tier 1\"\n    },\n    {\n        id: 4,\n        name: \"Apple\",\n        logo: \"https://via.placeholder.com/48x48/007AFF/FFFFFF?text=A\",\n        description: \"A multinational technology company that designs and develops consumer electronics, computer software, and online services.\",\n        industry: \"Technology & Consumer Electronics\",\n        size: \"165,000+ employees\",\n        founded: \"1976\",\n        location: \"Cupertino, CA\",\n        website: \"https://apple.com\",\n        totalActiveJobs: 1,\n        totalApplicants: 890,\n        totalHired: 45,\n        awaitedApproval: 1,\n        campus_recruiting: true,\n        tier: \"Tier 1\"\n    },\n    {\n        id: 5,\n        name: \"Netflix\",\n        logo: \"https://via.placeholder.com/48x48/E50914/FFFFFF?text=N\",\n        description: \"A streaming entertainment service with over 200 million paid memberships in over 190 countries.\",\n        industry: \"Entertainment & Media\",\n        size: \"12,000+ employees\",\n        founded: \"1997\",\n        location: \"Los Gatos, CA\",\n        website: \"https://netflix.com\",\n        totalActiveJobs: 1,\n        totalApplicants: 567,\n        totalHired: 23,\n        awaitedApproval: 0,\n        campus_recruiting: true,\n        tier: \"Tier 2\"\n    },\n    {\n        id: 6,\n        name: \"Cisco\",\n        logo: \"https://via.placeholder.com/48x48/1BA0D7/FFFFFF?text=C\",\n        description: \"A multinational digital communications technology conglomerate corporation headquartered in San Jose, California.\",\n        industry: \"Networking & Security\",\n        size: \"80,000+ employees\",\n        founded: \"1984\",\n        location: \"San Jose, CA\",\n        website: \"https://cisco.com\",\n        totalActiveJobs: 1,\n        totalApplicants: 445,\n        totalHired: 34,\n        awaitedApproval: 1,\n        campus_recruiting: true,\n        tier: \"Tier 2\"\n    },\n    {\n        id: 7,\n        name: \"AI Forge\",\n        logo: \"https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI\",\n        description: \"A cutting-edge AI research and development company specializing in machine learning and artificial intelligence solutions.\",\n        industry: \"Artificial Intelligence\",\n        size: \"500+ employees\",\n        founded: \"2018\",\n        location: \"San Francisco, CA\",\n        website: \"https://aiforge.com\",\n        totalActiveJobs: 3,\n        totalApplicants: 234,\n        totalHired: 18,\n        awaitedApproval: 2,\n        campus_recruiting: true,\n        tier: \"Tier 2\"\n    },\n    {\n        id: 8,\n        name: \"Cloudify\",\n        logo: \"https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL\",\n        description: \"A cloud infrastructure and DevOps automation company providing enterprise cloud orchestration solutions.\",\n        industry: \"Cloud Computing\",\n        size: \"300+ employees\",\n        founded: \"2012\",\n        location: \"Tel Aviv, Israel\",\n        website: \"https://cloudify.co\",\n        totalActiveJobs: 3,\n        totalApplicants: 189,\n        totalHired: 15,\n        awaitedApproval: 1,\n        campus_recruiting: true,\n        tier: \"Tier 3\"\n    },\n    {\n        id: 9,\n        name: \"InnoWare\",\n        logo: \"https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=IW\",\n        description: \"An innovative software development company creating next-generation applications and platforms.\",\n        industry: \"Software Development\",\n        size: \"250+ employees\",\n        founded: \"2015\",\n        location: \"Austin, TX\",\n        website: \"https://innovware.tech\",\n        totalActiveJobs: 2,\n        totalApplicants: 156,\n        totalHired: 12,\n        awaitedApproval: 1,\n        campus_recruiting: true,\n        tier: \"Tier 3\"\n    }\n];\n// Function to fetch companies from the API\nconst fetchCompanies = async (params = {})=>{\n    try {\n        // Add cache busting parameter\n        const fetchParams = {\n            ...params,\n            _t: new Date().getTime()\n        };\n        console.log('Fetching companies with cache busting...');\n        const response = await (0,_api_companies__WEBPACK_IMPORTED_MODULE_0__.listCompanies)(fetchParams);\n        // Handle various response formats\n        let companiesData = [];\n        if (response.data && Array.isArray(response.data)) {\n            companiesData = response.data;\n        } else if (response.data && response.data.results && Array.isArray(response.data.results)) {\n            companiesData = response.data.results;\n        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n            companiesData = response.data.data;\n        }\n        // Transform the data to match our frontend structure\n        const transformedData = companiesData.map(_api_companies__WEBPACK_IMPORTED_MODULE_0__.transformCompanyData);\n        console.log(`Fetched ${transformedData.length} companies from API`);\n        // Only fall back to static data if we got nothing from the API\n        if (transformedData.length === 0) {\n            console.warn('API returned empty companies array, using static data');\n            return companies;\n        }\n        return transformedData;\n    } catch (error) {\n        console.error('Error fetching companies:', error);\n        // Try once more with a different endpoint format\n        try {\n            console.log('Trying alternate endpoint format...');\n            const altResponse = await fetch('/api/v1/college/default-college/companies/');\n            if (altResponse.ok) {\n                const data = await altResponse.json();\n                const altData = Array.isArray(data) ? data : data.data || data.results || [];\n                if (altData.length > 0) {\n                    console.log('Successfully retrieved companies from alternate endpoint');\n                    return altData.map(_api_companies__WEBPACK_IMPORTED_MODULE_0__.transformCompanyData);\n                }\n            }\n        } catch (altError) {\n            console.error('Alternate endpoint also failed:', altError);\n        }\n        // Return static data as final fallback\n        return companies;\n    }\n};\n// Function to get a company by ID\nconst getCompanyById = async (id)=>{\n    try {\n        // First try to get from API\n        const response = await (0,_api_companies__WEBPACK_IMPORTED_MODULE_0__.getCompany)(id);\n        return (0,_api_companies__WEBPACK_IMPORTED_MODULE_0__.transformCompanyData)(response.data);\n    } catch (error) {\n        console.error(`Error fetching company ${id}:`, error);\n        // Fallback to static data\n        return companies.find((company)=>company.id === id) || null;\n    }\n};\nconst jobPostings = [\n    {\n        id: 1,\n        title: \"Software Engineering Intern\",\n        company_id: 1,\n        company: \"Google\",\n        logo: \"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G\",\n        location: \"Mountain View, CA\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 8000,\n        salary_max: 10000,\n        per: \"month\",\n        posted_date: \"2024-01-15\",\n        deadline: \"2024-03-15\",\n        description: \"Join our engineering team to work on large-scale systems that impact billions of users worldwide. You'll collaborate with senior engineers on critical infrastructure projects.\",\n        requirements: [\n            \"Currently pursuing CS, EE, or related field\",\n            \"Strong programming skills in Java, C++, or Python\",\n            \"Understanding of data structures and algorithms\",\n            \"GPA of 3.5 or higher\"\n        ],\n        skills: [\n            \"Java\",\n            \"Python\",\n            \"Data Structures\",\n            \"Algorithms\",\n            \"System Design\"\n        ],\n        benefits: [\n            \"Competitive salary\",\n            \"Housing stipend\",\n            \"Free meals\",\n            \"Mentorship program\",\n            \"Return offer potential\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: true,\n        is_featured: true,\n        is_active: true,\n        on_campus: true\n    },\n    {\n        id: 2,\n        title: \"Cloud Engineering Intern\",\n        company_id: 1,\n        company: \"Google\",\n        logo: \"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G\",\n        location: \"Seattle, WA\",\n        type: \"INTERNSHIP\",\n        mode: \"Hybrid\",\n        duration: \"10 weeks\",\n        salary_min: 7800,\n        salary_max: 9500,\n        per: \"month\",\n        posted_date: \"2024-01-20\",\n        deadline: \"2024-03-20\",\n        description: \"Work on Google Cloud Platform services and infrastructure. Build scalable solutions for enterprise customers.\",\n        requirements: [\n            \"Currently pursuing CS or related field\",\n            \"Experience with cloud platforms (AWS, GCP, Azure)\",\n            \"Knowledge of containerization (Docker, Kubernetes)\",\n            \"Strong problem-solving skills\"\n        ],\n        skills: [\n            \"Google Cloud Platform\",\n            \"Kubernetes\",\n            \"Docker\",\n            \"Python\",\n            \"Go\"\n        ],\n        benefits: [\n            \"Competitive compensation\",\n            \"Cloud platform access\",\n            \"Training and certifications\",\n            \"Team collaboration\",\n            \"Innovation time\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: true,\n        is_featured: false,\n        is_active: true,\n        on_campus: false\n    },\n    {\n        id: 3,\n        title: \"Machine Learning Research Intern\",\n        company_id: 1,\n        company: \"Google\",\n        logo: \"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=G\",\n        location: \"Mountain View, CA\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"14 weeks\",\n        salary_min: 9000,\n        salary_max: 11000,\n        per: \"month\",\n        posted_date: \"2024-01-25\",\n        deadline: \"2024-03-25\",\n        description: \"Join Google Research to work on cutting-edge ML projects. Contribute to publications and open-source projects.\",\n        requirements: [\n            \"Currently pursuing MS/PhD in CS, ML, or related field\",\n            \"Strong background in machine learning\",\n            \"Experience with TensorFlow or PyTorch\",\n            \"Research experience preferred\"\n        ],\n        skills: [\n            \"Machine Learning\",\n            \"TensorFlow\",\n            \"PyTorch\",\n            \"Python\",\n            \"Research\"\n        ],\n        benefits: [\n            \"Research environment\",\n            \"Publication opportunities\",\n            \"Mentorship from experts\",\n            \"Conference attendance\",\n            \"Open source contributions\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: true,\n        is_featured: true,\n        is_active: true,\n        on_campus: true\n    },\n    {\n        id: 4,\n        title: \"Data Science Intern\",\n        company_id: 2,\n        company: \"Microsoft\",\n        logo: \"https://via.placeholder.com/48x48/00A1F1/FFFFFF?text=M\",\n        location: \"Seattle, WA\",\n        type: \"INTERNSHIP\",\n        mode: \"Hybrid\",\n        duration: \"10 weeks\",\n        salary_min: 7500,\n        salary_max: 9000,\n        per: \"month\",\n        posted_date: \"2024-01-12\",\n        deadline: \"2024-02-28\",\n        description: \"Work with our data science team to develop machine learning models that power Microsoft's cloud services and improve user experiences.\",\n        requirements: [\n            \"Currently pursuing MS/PhD in Data Science, Statistics, or related field\",\n            \"Experience with Python, R, or SQL\",\n            \"Knowledge of machine learning frameworks\",\n            \"Strong analytical and problem-solving skills\"\n        ],\n        skills: [\n            \"Python\",\n            \"R\",\n            \"SQL\",\n            \"Machine Learning\",\n            \"TensorFlow\",\n            \"Azure\"\n        ],\n        benefits: [\n            \"Competitive compensation\",\n            \"Relocation assistance\",\n            \"Health benefits\",\n            \"Professional development\",\n            \"Networking opportunities\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: true,\n        is_featured: true,\n        is_active: true,\n        on_campus: true\n    },\n    {\n        id: 5,\n        title: \"Azure Developer Intern\",\n        company_id: 2,\n        company: \"Microsoft\",\n        logo: \"https://via.placeholder.com/48x48/00A1F1/FFFFFF?text=M\",\n        location: \"Redmond, WA\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 7200,\n        salary_max: 8800,\n        per: \"month\",\n        posted_date: \"2024-01-18\",\n        deadline: \"2024-03-10\",\n        description: \"Develop and maintain Azure cloud services. Work on scalable solutions for enterprise customers worldwide.\",\n        requirements: [\n            \"Currently pursuing CS or related field\",\n            \"Experience with cloud development\",\n            \"Knowledge of C# or .NET\",\n            \"Understanding of web services\"\n        ],\n        skills: [\n            \"Azure\",\n            \"C#\",\n            \".NET\",\n            \"Cloud Computing\",\n            \"REST APIs\"\n        ],\n        benefits: [\n            \"Azure credits\",\n            \"Microsoft certifications\",\n            \"Mentorship program\",\n            \"Career development\",\n            \"Innovation projects\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: true,\n        is_featured: false,\n        is_active: true,\n        on_campus: true\n    },\n    {\n        id: 6,\n        title: \"Product Management Intern\",\n        company_id: 3,\n        company: \"Amazon\",\n        logo: \"https://via.placeholder.com/48x48/FF9900/FFFFFF?text=A\",\n        location: \"Austin, TX\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 6500,\n        salary_max: 8000,\n        per: \"month\",\n        posted_date: \"2024-01-10\",\n        deadline: \"2024-03-01\",\n        description: \"Drive product strategy and execution for Amazon's consumer-facing products. Work directly with engineering, design, and business teams.\",\n        requirements: [\n            \"Currently pursuing MBA or relevant undergraduate degree\",\n            \"Strong analytical and communication skills\",\n            \"Experience with product management tools\",\n            \"Customer-obsessed mindset\"\n        ],\n        skills: [\n            \"Product Strategy\",\n            \"Analytics\",\n            \"SQL\",\n            \"A/B Testing\",\n            \"User Research\"\n        ],\n        benefits: [\n            \"Competitive salary\",\n            \"Stock options\",\n            \"Professional mentorship\",\n            \"Leadership training\",\n            \"Full-time offer potential\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: true,\n        is_featured: false,\n        is_active: true,\n        on_campus: true\n    },\n    {\n        id: 7,\n        title: \"AWS Solutions Architect Intern\",\n        company_id: 3,\n        company: \"Amazon\",\n        logo: \"https://via.placeholder.com/48x48/FF9900/FFFFFF?text=A\",\n        location: \"Arlington, VA\",\n        type: \"INTERNSHIP\",\n        mode: \"Hybrid\",\n        duration: \"10 weeks\",\n        salary_min: 7000,\n        salary_max: 8500,\n        per: \"month\",\n        posted_date: \"2024-01-22\",\n        deadline: \"2024-03-15\",\n        description: \"Help customers architect and deploy cloud solutions on AWS. Work with enterprise clients on their cloud transformation journey.\",\n        requirements: [\n            \"Currently pursuing CS, IT, or related field\",\n            \"Basic understanding of cloud computing\",\n            \"Strong communication skills\",\n            \"Interest in customer-facing roles\"\n        ],\n        skills: [\n            \"AWS\",\n            \"Cloud Architecture\",\n            \"Solution Design\",\n            \"Customer Relations\"\n        ],\n        benefits: [\n            \"AWS certifications\",\n            \"Customer interaction\",\n            \"Cloud expertise\",\n            \"Professional growth\",\n            \"Networking opportunities\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: true,\n        on_campus: false\n    },\n    {\n        id: 8,\n        title: \"UX Design Intern\",\n        company_id: 4,\n        company: \"Apple\",\n        logo: \"https://via.placeholder.com/48x48/007AFF/FFFFFF?text=A\",\n        location: \"Cupertino, CA\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 7000,\n        salary_max: 8500,\n        per: \"month\",\n        posted_date: \"2024-01-08\",\n        deadline: \"2024-02-20\",\n        description: \"Create innovative user experiences for Apple's next-generation products. Work alongside world-class designers and engineers.\",\n        requirements: [\n            \"Currently pursuing degree in Design, HCI, or related field\",\n            \"Proficiency in Figma, Sketch, or similar tools\",\n            \"Portfolio demonstrating design thinking\",\n            \"Strong collaboration skills\"\n        ],\n        skills: [\n            \"Figma\",\n            \"Sketch\",\n            \"Prototyping\",\n            \"User Research\",\n            \"Design Systems\"\n        ],\n        benefits: [\n            \"Competitive compensation\",\n            \"Employee discounts\",\n            \"Wellness programs\",\n            \"Creative environment\",\n            \"Career development\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: false,\n        is_featured: true,\n        is_active: true,\n        on_campus: true\n    },\n    {\n        id: 9,\n        title: \"Marketing Analytics Intern\",\n        company_id: 5,\n        company: \"Netflix\",\n        logo: \"https://via.placeholder.com/48x48/E50914/FFFFFF?text=N\",\n        location: \"Los Gatos, CA\",\n        type: \"INTERNSHIP\",\n        mode: \"Hybrid\",\n        duration: \"10 weeks\",\n        salary_min: 6000,\n        salary_max: 7500,\n        per: \"month\",\n        posted_date: \"2024-01-05\",\n        deadline: \"2024-02-15\",\n        description: \"Analyze marketing campaign performance and user engagement metrics to drive growth strategies for Netflix's global platform.\",\n        requirements: [\n            \"Currently pursuing degree in Marketing, Business, or Analytics\",\n            \"Experience with Excel, SQL, and data visualization tools\",\n            \"Strong quantitative analysis skills\",\n            \"Interest in entertainment industry\"\n        ],\n        skills: [\n            \"SQL\",\n            \"Excel\",\n            \"Tableau\",\n            \"Google Analytics\",\n            \"A/B Testing\"\n        ],\n        benefits: [\n            \"Competitive pay\",\n            \"Netflix subscription\",\n            \"Flexible hours\",\n            \"Mentorship\",\n            \"Networking events\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: true,\n        on_campus: false\n    },\n    {\n        id: 10,\n        title: \"Cybersecurity Intern\",\n        company_id: 6,\n        company: \"Cisco\",\n        logo: \"https://via.placeholder.com/48x48/1BA0D7/FFFFFF?text=C\",\n        location: \"San Jose, CA\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 6500,\n        salary_max: 8000,\n        per: \"month\",\n        posted_date: \"2024-01-03\",\n        deadline: \"2024-02-28\",\n        description: \"Join our security team to develop and implement cybersecurity solutions that protect enterprise networks and infrastructure.\",\n        requirements: [\n            \"Currently pursuing degree in Cybersecurity, CS, or related field\",\n            \"Knowledge of network security protocols\",\n            \"Understanding of threat analysis\",\n            \"Security certifications preferred\"\n        ],\n        skills: [\n            \"Network Security\",\n            \"Python\",\n            \"Linux\",\n            \"Penetration Testing\",\n            \"SIEM\"\n        ],\n        benefits: [\n            \"Competitive salary\",\n            \"Security training\",\n            \"Certification support\",\n            \"Professional growth\",\n            \"Team collaboration\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: true,\n        is_featured: false,\n        is_active: true,\n        on_campus: true\n    },\n    // AI Forge Jobs\n    {\n        id: 11,\n        title: \"Cloud Engineering Intern\",\n        company_id: 7,\n        company: \"AI Forge\",\n        logo: \"https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI\",\n        location: \"Hyderabad\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"16 weeks\",\n        salary_min: 10146,\n        salary_max: 13914,\n        per: \"month\",\n        posted_date: \"2025-05-20\",\n        deadline: \"2025-07-25\",\n        description: \"Join our cutting-edge cloud engineering team and work on scalable infrastructure solutions. You'll be working with modern technologies including Kubernetes, Docker, and cloud platforms to build the next generation of cloud applications.\",\n        requirements: [\n            \"Currently pursuing CS, IT, or related field\",\n            \"Experience with cloud platforms\",\n            \"Knowledge of containerization\",\n            \"Strong programming skills\"\n        ],\n        skills: [\n            \"PyTorch\",\n            \"TensorFlow\",\n            \"Kubernetes\",\n            \"CI/CD\",\n            \"Scrum\"\n        ],\n        benefits: [\n            \"Competitive stipend\",\n            \"Technical mentorship\",\n            \"Industry exposure\",\n            \"Certification opportunities\",\n            \"Full-time conversion possibility\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: false,\n        on_campus: true\n    },\n    {\n        id: 12,\n        title: \"AR/VR Developer Intern\",\n        company_id: 7,\n        company: \"AI Forge\",\n        logo: \"https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI\",\n        location: \"Mumbai\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 10022,\n        salary_max: 13721,\n        per: \"month\",\n        posted_date: \"2025-05-19\",\n        deadline: \"2025-06-20\",\n        description: \"Build immersive AR/VR experiences using cutting-edge technologies. Work on projects that blend physical and digital worlds.\",\n        requirements: [\n            \"Currently pursuing CS, Game Dev, or related field\",\n            \"Experience with Unity or Unreal Engine\",\n            \"Knowledge of 3D graphics\",\n            \"Creative problem-solving skills\"\n        ],\n        skills: [\n            \"Linux\",\n            \"Angular\",\n            \"Azure\",\n            \"JIRA\",\n            \"NLP\",\n            \"AWS\",\n            \"TypeScript\",\n            \"Firebase\"\n        ],\n        benefits: [\n            \"Cutting-edge technology\",\n            \"Creative projects\",\n            \"Industry mentorship\",\n            \"Portfolio development\",\n            \"Innovation opportunities\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: false,\n        on_campus: false\n    },\n    {\n        id: 13,\n        title: \"UI/UX Designer Intern\",\n        company_id: 7,\n        company: \"AI Forge\",\n        logo: \"https://via.placeholder.com/48x48/6B46C1/FFFFFF?text=AI\",\n        location: \"Hyderabad\",\n        type: \"INTERNSHIP\",\n        mode: \"Hybrid\",\n        duration: \"14 weeks\",\n        salary_min: 13860,\n        salary_max: 20446,\n        per: \"month\",\n        posted_date: \"2025-05-22\",\n        deadline: \"2025-06-30\",\n        description: \"Design intuitive and beautiful user interfaces that delight users. Work on both web and mobile applications with a focus on user experience.\",\n        requirements: [\n            \"Currently pursuing Design, HCI, or related field\",\n            \"Proficiency in design tools\",\n            \"Understanding of UX principles\",\n            \"Portfolio of design work\"\n        ],\n        skills: [\n            \"Kotlin\",\n            \"Jenkins\",\n            \"Python\",\n            \"Java\",\n            \"NLP\",\n            \"Kubernetes\",\n            \"Swift\"\n        ],\n        benefits: [\n            \"Design mentorship\",\n            \"Portfolio building\",\n            \"User research exposure\",\n            \"Creative freedom\",\n            \"Industry connections\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: true,\n        on_campus: false\n    },\n    // Cloudify Jobs\n    {\n        id: 14,\n        title: \"SEO Specialist Intern\",\n        company_id: 8,\n        company: \"Cloudify\",\n        logo: \"https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL\",\n        location: \"Hyderabad\",\n        type: \"INTERNSHIP\",\n        mode: \"Remote\",\n        duration: \"10 weeks\",\n        salary_min: 8826,\n        salary_max: 13341,\n        per: \"month\",\n        posted_date: \"2025-05-21\",\n        deadline: \"2025-07-20\",\n        description: \"Drive organic growth through strategic SEO initiatives. Work with content teams to optimize website performance and implement data-driven SEO strategies.\",\n        requirements: [\n            \"Currently pursuing Marketing, Communications, or related field\",\n            \"Basic understanding of SEO principles\",\n            \"Experience with analytics tools\",\n            \"Strong analytical skills\"\n        ],\n        skills: [\n            \"GraphQL\",\n            \"MongoDB\",\n            \"Angular\",\n            \"PostgreSQL\",\n            \"Scrum\",\n            \"Azure\"\n        ],\n        benefits: [\n            \"Remote work flexibility\",\n            \"SEO expertise development\",\n            \"Analytics training\",\n            \"Marketing exposure\",\n            \"Growth opportunities\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: false,\n        on_campus: false\n    },\n    {\n        id: 15,\n        title: \"Business Analyst Intern\",\n        company_id: 8,\n        company: \"Cloudify\",\n        logo: \"https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL\",\n        location: \"Bangalore\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 11333,\n        salary_max: 14772,\n        per: \"month\",\n        posted_date: \"2025-05-20\",\n        deadline: \"2025-07-14\",\n        description: \"Analyze business processes and drive data-driven decision making. Work directly with stakeholders to identify opportunities for improvement.\",\n        requirements: [\n            \"Currently pursuing Business, Economics, or related field\",\n            \"Strong analytical and communication skills\",\n            \"Experience with data analysis tools\",\n            \"Business acumen\"\n        ],\n        skills: [\n            \"MySQL\",\n            \"Firebase\",\n            \"Angular\",\n            \"Python\",\n            \"PyTorch\",\n            \"MongoDB\"\n        ],\n        benefits: [\n            \"Business strategy exposure\",\n            \"Stakeholder interaction\",\n            \"Data analysis skills\",\n            \"Process improvement\",\n            \"Leadership development\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: false,\n        on_campus: true\n    },\n    {\n        id: 16,\n        title: \"Product Management Intern\",\n        company_id: 8,\n        company: \"Cloudify\",\n        logo: \"https://via.placeholder.com/48x48/10B981/FFFFFF?text=CL\",\n        location: \"Mumbai\",\n        type: \"INTERNSHIP\",\n        mode: \"Hybrid\",\n        duration: \"14 weeks\",\n        salary_min: 8729,\n        salary_max: 15013,\n        per: \"month\",\n        posted_date: \"2025-05-22\",\n        deadline: \"2025-07-15\",\n        description: \"Drive product strategy and work cross-functionally to deliver exceptional user experiences. Learn the fundamentals of product management.\",\n        requirements: [\n            \"Currently pursuing Business, CS, or related field\",\n            \"Strong communication and analytical skills\",\n            \"Interest in product development\",\n            \"User-centric mindset\"\n        ],\n        skills: [\n            \"Scrum\",\n            \"Python\",\n            \"Git\",\n            \"Agile\",\n            \"PostgreSQL\"\n        ],\n        benefits: [\n            \"Product strategy learning\",\n            \"Cross-functional collaboration\",\n            \"User research exposure\",\n            \"Agile methodology\",\n            \"Leadership skills\"\n        ],\n        remote_eligible: true,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: true,\n        on_campus: true\n    },\n    // InnoWare Jobs\n    {\n        id: 17,\n        title: \"AI Research Intern\",\n        company_id: 9,\n        company: \"InnoWare\",\n        logo: \"https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=IW\",\n        location: \"Hyderabad\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"16 weeks\",\n        salary_min: 12242,\n        salary_max: 16109,\n        per: \"month\",\n        posted_date: \"2025-05-23\",\n        deadline: \"2025-07-27\",\n        description: \"Contribute to cutting-edge AI research projects and work with state-of-the-art machine learning technologies.\",\n        requirements: [\n            \"Currently pursuing MS/PhD in CS, AI, or related field\",\n            \"Strong background in machine learning\",\n            \"Research experience preferred\",\n            \"Programming skills in Python\"\n        ],\n        skills: [\n            \"Blockchain\",\n            \"JIRA\",\n            \"PostgreSQL\",\n            \"Django\",\n            \"Firebase\"\n        ],\n        benefits: [\n            \"Research opportunities\",\n            \"AI expertise development\",\n            \"Publication possibilities\",\n            \"Innovation projects\",\n            \"Academic collaboration\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: false,\n        on_campus: true\n    },\n    {\n        id: 18,\n        title: \"Robotics Intern\",\n        company_id: 9,\n        company: \"InnoWare\",\n        logo: \"https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=IW\",\n        location: \"Hyderabad\",\n        type: \"INTERNSHIP\",\n        mode: \"On-campus\",\n        duration: \"12 weeks\",\n        salary_min: 12822,\n        salary_max: 20721,\n        per: \"month\",\n        posted_date: \"2025-05-22\",\n        deadline: \"2025-07-04\",\n        description: \"Work on innovative robotics projects and develop automation solutions that will shape the future of industry.\",\n        requirements: [\n            \"Currently pursuing Robotics, ME, CS, or related field\",\n            \"Experience with robotics platforms\",\n            \"Programming skills\",\n            \"Hardware/software integration knowledge\"\n        ],\n        skills: [\n            \"Django\",\n            \"SEO\",\n            \"Node.js\",\n            \"Express\",\n            \"Docker\"\n        ],\n        benefits: [\n            \"Hands-on robotics\",\n            \"Innovation projects\",\n            \"Hardware experience\",\n            \"Automation expertise\",\n            \"Industry applications\"\n        ],\n        remote_eligible: false,\n        sponsorship_available: false,\n        is_featured: false,\n        is_active: true,\n        on_campus: false\n    }\n];\n// Student applications data (for My Jobs page)\nconst studentApplications = [\n    {\n        id: 1,\n        job_id: 11,\n        title: \"Cloud Engineering Intern\",\n        company: \"AI Forge\",\n        description: \"Join our cutting-edge cloud engineering team and work on scalable infrastructure solutions.\",\n        location: \"Hyderabad\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 10146,\n        salary_max: 13914,\n        required_skills: \"PyTorch, TensorFlow, Kubernetes, CI/CD, Scrum\",\n        application_deadline: \"2025-07-25\",\n        is_active: false,\n        on_campus: true,\n        status: \"UNDER REVIEW\",\n        applied_at: \"2025-05-22T14:57:47.186021\",\n        updated_at: \"2025-05-28T14:57:47.186028\"\n    },\n    {\n        id: 2,\n        job_id: 14,\n        title: \"SEO Specialist Intern\",\n        company: \"Cloudify\",\n        description: \"Drive organic growth through strategic SEO initiatives.\",\n        location: \"Hyderabad\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 8826,\n        salary_max: 13341,\n        required_skills: \"GraphQL, MongoDB, Angular, PostgreSQL, Scrum, Azure\",\n        application_deadline: \"2025-07-20\",\n        is_active: false,\n        on_campus: false,\n        status: \"REJECTED\",\n        applied_at: \"2025-05-23T14:57:47.186257\",\n        updated_at: \"2025-05-28T14:57:47.186260\"\n    },\n    {\n        id: 3,\n        job_id: 15,\n        title: \"Business Analyst Intern\",\n        company: \"Cloudify\",\n        description: \"Analyze business processes and drive data-driven decision making.\",\n        location: \"Bangalore\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 11333,\n        salary_max: 14772,\n        required_skills: \"MySQL, Firebase, Angular, Python, PyTorch, MongoDB\",\n        application_deadline: \"2025-07-14\",\n        is_active: false,\n        on_campus: true,\n        status: \"REJECTED\",\n        applied_at: \"2025-05-23T14:57:47.186430\",\n        updated_at: \"2025-05-28T14:57:47.186434\"\n    },\n    {\n        id: 4,\n        job_id: 17,\n        title: \"AI Research Intern\",\n        company: \"InnoWare\",\n        description: \"Contribute to cutting-edge AI research projects.\",\n        location: \"Hyderabad\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 12242,\n        salary_max: 16109,\n        required_skills: \"Blockchain, JIRA, PostgreSQL, Django, Firebase\",\n        application_deadline: \"2025-07-27\",\n        is_active: false,\n        on_campus: true,\n        status: \"APPLIED\",\n        applied_at: \"2025-05-25T14:57:47.186581\",\n        updated_at: \"2025-05-28T14:57:47.186584\"\n    },\n    {\n        id: 5,\n        job_id: 18,\n        title: \"Robotics Intern\",\n        company: \"InnoWare\",\n        description: \"Work on innovative robotics projects.\",\n        location: \"Hyderabad\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 12822,\n        salary_max: 20721,\n        required_skills: \"Django, SEO, Node.js, Express, Docker\",\n        application_deadline: \"2025-07-04\",\n        is_active: true,\n        on_campus: false,\n        status: \"APPLIED\",\n        applied_at: \"2025-05-24T14:57:47.186683\",\n        updated_at: \"2025-05-28T14:57:47.186686\"\n    },\n    {\n        id: 6,\n        job_id: 12,\n        title: \"AR/VR Developer Intern\",\n        company: \"AI Forge\",\n        description: \"Build immersive AR/VR experiences using cutting-edge technologies.\",\n        location: \"Mumbai\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 10022,\n        salary_max: 13721,\n        required_skills: \"Linux, Angular, Azure, JIRA, NLP, AWS, TypeScript, Firebase\",\n        application_deadline: \"2025-06-20\",\n        is_active: false,\n        on_campus: false,\n        status: \"INTERVIEW SCHEDULED\",\n        applied_at: \"2025-05-21T14:57:47.186778\",\n        updated_at: \"2025-05-28T14:57:47.186781\"\n    },\n    {\n        id: 7,\n        job_id: 16,\n        title: \"Product Management Intern\",\n        company: \"Cloudify\",\n        description: \"Drive product strategy and work cross-functionally to deliver exceptional user experiences.\",\n        location: \"Mumbai\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 8729,\n        salary_max: 15013,\n        required_skills: \"Scrum, Python, Git, Agile, PostgreSQL\",\n        application_deadline: \"2025-07-15\",\n        is_active: true,\n        on_campus: true,\n        status: \"INTERVIEW SCHEDULED\",\n        applied_at: \"2025-05-24T14:57:47.187017\",\n        updated_at: \"2025-05-28T14:57:47.187023\"\n    },\n    {\n        id: 8,\n        job_id: 13,\n        title: \"UI/UX Designer Intern\",\n        company: \"AI Forge\",\n        description: \"Design intuitive and beautiful user interfaces that delight users.\",\n        location: \"Hyderabad\",\n        job_type: \"INTERNSHIP\",\n        salary_min: 13860,\n        salary_max: 20446,\n        required_skills: \"Kotlin, Jenkins, Python, Java, NLP, Kubernetes, Swift\",\n        application_deadline: \"2025-06-30\",\n        is_active: true,\n        on_campus: false,\n        status: \"INTERVIEW SCHEDULED\",\n        applied_at: \"2025-05-24T14:57:47.187473\",\n        updated_at: \"2025-05-28T14:57:47.187476\"\n    }\n];\n// Helper functions\nconst getActiveJobs = ()=>{\n    return jobPostings.filter((job)=>job.is_active);\n};\nconst getJobById = (jobId)=>{\n    return jobPostings.find((job)=>job.id === jobId);\n};\nconst getCompaniesWithActiveJobs = ()=>{\n    const activeJobCompanyIds = new Set(jobPostings.filter((job)=>job.is_active).map((job)=>job.company_id));\n    return companies.filter((company)=>activeJobCompanyIds.has(company.id));\n};\nconst getApplicationStats = ()=>{\n    const total = studentApplications.length;\n    const pending = studentApplications.filter((app)=>app.status === 'APPLIED' || app.status === 'UNDER REVIEW').length;\n    const interviews = studentApplications.filter((app)=>app.status === 'INTERVIEW SCHEDULED').length;\n    const rejected = studentApplications.filter((app)=>app.status === 'REJECTED').length;\n    const accepted = studentApplications.filter((app)=>app.status === 'ACCEPTED').length;\n    return {\n        total,\n        pending,\n        interviews,\n        rejected,\n        accepted\n    };\n};\nconst getJobStats = ()=>{\n    const total = jobPostings.length;\n    const active = jobPostings.filter((job)=>job.is_active).length;\n    const internships = jobPostings.filter((job)=>job.type === 'INTERNSHIP').length;\n    const fullTime = jobPostings.filter((job)=>job.type === 'FULL_TIME').length;\n    const remote = jobPostings.filter((job)=>job.remote_eligible).length;\n    const featured = jobPostings.filter((job)=>job.is_featured).length;\n    return {\n        total,\n        active,\n        internships,\n        fullTime,\n        remote,\n        featured\n    };\n};\n// Function to get jobs by company\nfunction getJobsByCompany(companyId) {\n    // This is a placeholder implementation\n    // You should replace this with an actual API call to fetch jobs by company ID\n    // For now, we'll return an empty array to prevent the error\n    console.log(`Fetching jobs for company ID: ${companyId}`);\n    return [];\n// When you have the API endpoint ready, implement something like:\n// return client.get(`/api/v1/companies/${companyId}/jobs/`)\n//   .then(response => response.data)\n//   .catch(error => {\n//     console.error(`Error fetching jobs for company ${companyId}:`, error);\n//     return [];\n//   });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/jobsData.js\n");

/***/ })

};
;