from rest_framework import serializers
from .models import JobPosting, JobApplication
from accounts.serializers import UserSerializer
# EmployerProfile removed
from .models import CompanyForm



class JobPostingListSerializer(serializers.ModelSerializer):
    """Serializer for job posting list view."""
    company_name = serializers.SerializerMethodField()
    
    class Meta:
        model = JobPosting
        fields = ('id', 'title', 'location', 'job_type', 'company_name',
                  'salary_min', 'salary_max', 'application_deadline', 'is_active', 'is_published')
    
    def get_company_name(self, obj):
        return obj.company.name if obj.company else None


class JobPostingDetailSerializer(serializers.ModelSerializer):
    """Serializer for job posting detail view."""
    company_name = serializers.SerializerMethodField()
    
    class Meta:
        model = JobPosting
        fields = ('id', 'title', 'description', 'location', 'job_type', 
                  'company_name', 'salary_min', 'salary_max', 
                  'required_skills', 'application_deadline', 
                  'is_active', 'is_published', 'created_at', 'updated_at')
    
    def get_company_name(self, obj):
        return obj.company.name if obj.company else None


class JobPostingCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for job posting create/update views."""
    class Meta:
        model = JobPosting
        fields = ('id', 'title', 'description', 'location', 'job_type', 
                  'salary_min', 'salary_max', 'required_skills', 
                  'application_deadline', 'is_active', 'is_published')
        read_only_fields = ('id',)
    
    def create(self, validated_data):
        # Note: For now, we'll handle company assignment in the view
        return super().create(validated_data)

class JobApplicationSerializer(serializers.ModelSerializer):
    applicant = UserSerializer(read_only=True)
    job_title = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = JobApplication
        fields = (
            'id', 'job', 'job_title', 'applicant', 'cover_letter', 
            'resume', 'status', 'applied_at', 'updated_at'
        )
        read_only_fields = ('id', 'job_title', 'applied_at', 'updated_at', 'applicant', 'resume')

    def get_job_title(self, obj):
        return obj.job.title

    def update(self, instance, validated_data):
        request = self.context['request']
        user = request.user
        job = instance.job

        if job.on_campus and not user.is_staff:
            raise serializers.ValidationError("Only admin can update status for on-campus jobs.")
        # Skip employer check for now since we're using companies directly
        # TODO: Implement proper company-based permissions

        return super().update(instance, validated_data)
    

# jobs/serializers.py
class JobWithApplicationStatsSerializer(serializers.ModelSerializer):
    total_applicants = serializers.SerializerMethodField()
    total_hired = serializers.SerializerMethodField()
    company_name = serializers.SerializerMethodField()

    class Meta:
        model = JobPosting
        fields = [
            'id', 'title', 'location', 'job_type',
            'company_name', 'application_deadline',
            'total_applicants', 'total_hired', 'is_active', 'is_published'
        ]

    def get_total_applicants(self, obj):
        return obj.applications.count()

    def get_total_hired(self, obj):
        return obj.applications.filter(status='HIRED').count()

    def get_company_name(self, obj):
        return obj.company.name if obj.company else None


from rest_framework import serializers
from jobs.models import JobApplication

class StudentApplicationSerializer(serializers.ModelSerializer):
    job_id = serializers.IntegerField(source='job.id')
    title = serializers.CharField(source='job.title')
    company = serializers.CharField(source='job.company.name')
    description = serializers.CharField(source='job.description')
    location = serializers.CharField(source='job.location')
    job_type = serializers.CharField(source='job.job_type')
    salary_min = serializers.DecimalField(source='job.salary_min', max_digits=10, decimal_places=2)
    salary_max = serializers.DecimalField(source='job.salary_max', max_digits=10, decimal_places=2)
    required_skills = serializers.SerializerMethodField()
    application_deadline = serializers.DateField(source='job.application_deadline', format="%Y-%m-%d")
    is_active = serializers.BooleanField(source='job.is_active')
    on_campus = serializers.BooleanField(source='job.on_campus')
    applied_at = serializers.DateTimeField(format="%Y-%m-%dT%H:%M:%S.%f")
    updated_at = serializers.DateTimeField(format="%Y-%m-%dT%H:%M:%S.%f")

    class Meta:
        model = JobApplication
        fields = [
            'id', 'job_id', 'title', 'company', 'description', 'location',
            'job_type', 'salary_min', 'salary_max', 'required_skills',
            'application_deadline', 'is_active', 'on_campus', 'status',
            'applied_at', 'updated_at'
        ]

    def get_required_skills(self, obj):
        if hasattr(obj.job, 'skills') and obj.job.skills:
            return ', '.join(obj.job.skills)
        return ""


# Enhanced serializers for the comprehensive API
# CompanySerializer removed - use companies.serializers.CompanySerializer instead


class EnhancedJobSerializer(serializers.ModelSerializer):
    """Enhanced Job serializer matching the API schema"""
    company_name = serializers.SerializerMethodField()
    company_id = serializers.SerializerMethodField()
    requirements = serializers.SerializerMethodField()
    skills = serializers.ListField(
        child=serializers.CharField(), 
        allow_empty=True,
        required=False
    )
    benefits = serializers.ListField(
        child=serializers.CharField(), 
        allow_empty=True,
        required=False
    )
    duration = serializers.CharField(allow_blank=True, required=False)
    company = serializers.SerializerMethodField()
    
    class Meta:
        model = JobPosting
        fields = [
            'id', 'title', 'company_name', 'company_id', 'description', 
            'location', 'job_type', 'salary_min', 'salary_max', 'duration',
            'application_deadline', 'requirements', 'skills', 'benefits',
            'is_active', 'is_published', 'created_at', 'updated_at', 'company'
        ]
        
    def get_company_name(self, obj):
        return obj.company.name if obj.company else None
            
    def get_company_id(self, obj):
        return obj.company.id if obj.company else None
        
    def get_company(self, obj):
        if obj.company:
            from companies.serializers import CompanySerializer
            return CompanySerializer(obj.company).data
        return None

    def get_requirements(self, obj):
        """Convert required_skills string to list"""
        if obj.required_skills:
            return [req.strip() for req in obj.required_skills.split(',') if req.strip()]
        return []

    def to_representation(self, instance):
        """Convert string fields to lists for JSON response"""
        data = super().to_representation(instance)
        
        # Handle skills and benefits (these might need to be added to model or derived)
        data['skills'] = []
        data['benefits'] = []
        
        return data

    def to_internal_value(self, data):
        """Convert lists to strings for model storage"""
        if 'requirements' in data and isinstance(data['requirements'], list):
            data['required_skills'] = ', '.join(data['requirements'])
        
        return super().to_internal_value(data)

    def update(self, instance, validated_data):
        """Handle requirements field conversion during update"""
        # The validated_data already has required_skills from to_internal_value
        return super().update(instance, validated_data)

    def create(self, validated_data):
        """Handle requirements field conversion during create"""
        # The validated_data already has required_skills from to_internal_value
        return super().create(validated_data)


class EnhancedJobApplicationSerializer(serializers.ModelSerializer):
    """Enhanced Job Application serializer"""
    job_id = serializers.IntegerField(source='job.id', read_only=True)
    student_id = serializers.IntegerField(source='applicant.id', read_only=True)
    additional_field_responses = serializers.JSONField(source='applied_data_snapshot', default=dict, required=False)
    
    class Meta:
        model = JobApplication
        fields = [
            'id', 'job_id', 'student_id', 'cover_letter', 'status',
            'additional_field_responses', 'applied_at', 'updated_at'
        ]

    def create(self, validated_data):
        validated_data['applicant'] = self.context['request'].user
        return super().create(validated_data)


class JobListResponseSerializer(serializers.Serializer):
    """Paginated job list response"""
    data = EnhancedJobSerializer(many=True)
    pagination = serializers.DictField()


class StatsSerializer(serializers.Serializer):
    """Statistics serializer"""
    total_jobs = serializers.IntegerField()
    active_jobs = serializers.IntegerField()
    total_applications = serializers.IntegerField()
    pending_applications = serializers.IntegerField()
    total_companies = serializers.IntegerField()
    hiring_companies = serializers.IntegerField()


class CompanyFormSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyForm
        fields = ['id', 'company', 'key', 'created_at', 'submitted', 'details']
        read_only_fields = ['id', 'created_at']
        extra_kwargs = {
            'key': {'required': False},
            'submitted': {'required': False},
            'details': {'required': False}
        }


