'use client';

import React, { useState, useRef, useEffect, createContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { IconUser } from '@tabler/icons-react';
import { cn } from '../../utils/cn';


const SidebarContext = createContext();

export default function Sidebar({
  sections = [],
  bottomItems = [],
  defaultExpanded = false,
  navbarHeight = '4rem',
  className,
}) {
  const [expanded, setExpanded] = useState(defaultExpanded);

  // Helper function to render icons consistently
  const renderIcon = (icon) => {
    if (React.isValidElement(icon)) {
      // Clone the icon and ensure consistent sizing
      return React.cloneElement(icon, {
        className: 'w-7 h-7',
        size: undefined // Remove size prop if it exists
      });
    }
    return icon;
  };

  return (
    <SidebarContext.Provider value={{ expanded }}>
      <motion.div
        animate={{ width: expanded ? '300px' : '80px' }}
        onMouseEnter={() => setExpanded(true)}
        onMouseLeave={() => setExpanded(false)}
        className={cn(
          'fixed top-0 left-0 h-screen bg-white px-4 py-4 shadow-lg rounded-r-3xl flex flex-col justify-between',
          className
        )}
        style={{ marginTop: navbarHeight }}
      >
        <nav className="flex flex-col gap-8">
          {/* Logo */}
          <Link
            href="/"
            className={cn(
              'flex items-center gap-4 p-3 text-black',
              !expanded && 'justify-center'
            )}
          >
            <div className="flex-shrink-0 text-2xl font-bold">
              {!expanded ? 'P' : null}
            </div>
            {expanded && (
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-2xl font-bold whitespace-nowrap"
              >
                Placeeasy.in
              </motion.span>
            )}
          </Link>

          {/* Render top sections */}
          {sections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="bg-gray-50 rounded-xl p-2">
              <AnimatePresence>
                {section.items.map((item) => (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={cn(
                      'flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors',
                      !expanded && 'justify-center'
                    )}
                  >
                    <div className="flex-shrink-0 flex items-center justify-center">
                      {renderIcon(item.icon)}
                    </div>
                    {expanded && (
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-base font-bold whitespace-nowrap"
                      >
                        {item.title}
                      </motion.span>
                    )}
                  </Link>
                ))}
              </AnimatePresence>
            </div>
          ))}
        </nav>

        {/* Bottom Items */}
        {bottomItems.length > 0 && (
          <div className="mt-6">
            {bottomItems.map((item) => (
              <Link
                key={item.title}
                href={item.href}
                className={cn(
                  'flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors mb-2',
                  !expanded && 'justify-center'
                )}
              >
                <div className="flex-shrink-0 flex items-center justify-center">
                  {renderIcon(item.icon)}
                </div>
                {expanded && (
                  <motion.span
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="text-base font-bold whitespace-nowrap"
                  >
                    {item.title}
                  </motion.span>
                )}
              </Link>
            ))}
          </div>
        )}
      </motion.div>
    </SidebarContext.Provider>
  );
}

export function MenuBar({ menuItems = [], onItemClick }) {
  const [selectedItem, setSelectedItem] = useState(menuItems[0]?.label);

  const handleClick = (item) => {
    setSelectedItem(item.label);
    onItemClick?.(item);
  };

  return (

    <div className="w-48 bg-white h-full rounded-xl overflow-auto border-r">
      <ul className="space-y-6 p-6">
        {menuItems.map((item, index) => (
          <li
            key={index}
            className="cursor-pointer text-gray-700 text-lg 
                       hover:text-blue-500 hover:scale-105 
                       transition-transform duration-200"
            onClick={() => onItemClick?.(item)}
          >
            {item.label}
          </li>
        ))}
      </ul>
    </div>
  );
}

export function CompanyListPage() {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter companies based on search query
  const filteredCompanies = companiesData.filter(company =>
    company.companyName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-6 max-w-5xl mx-auto">
      <input
        type="text"
        placeholder="Search company name..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="w-full p-2 border rounded-lg mb-6"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredCompanies.length > 0 ? (
          filteredCompanies.map((company, index) => (
            <CompanyCard key={index} company={company} />
          ))
        ) : (
          <p className="text-center text-gray-500 col-span-full">
            No companies found.
          </p>
        )}
      </div>
    </div>
  );
}

export function CompanyCard({ company, onClick }) {
  const [imgError, setImgError] = useState(false);

  return (
    <div className="bg-gray-100 p-4 rounded-xl shadow w-full hover:shadow-lg transition-shadow">
      <div className="flex items-center gap-4">
        <div className="w-14 h-14 rounded bg-gray-300 flex items-center justify-center text-sm font-semibold text-gray-600 overflow-hidden">
          {!imgError ? (
            <img
              src={company.Logo}
              alt="Logo"
              className="w-full h-full object-cover"
              onError={() => setImgError(true)}
            />
          ) : (
            <span>Logo</span>
          )}
        </div>
        <div>
          <h3
            onClick={() => onClick(company)}
            className="text-lg font-semibold text-blue-500 cursor-pointer hover:underline"
          >
            {company.companyName}
          </h3>
          <p className="text-sm text-gray-500">{company.companyDescription}</p>
        </div>
      </div>
      <div className="mt-3 text-sm text-gray-700">
        <p>
          {company.totalActiveJobs} Active Listing, {company.awaitedApproval} Awaiting Approval
        </p><br></br>
        <p className="text-gray-500">{company.location}</p>
      </div>
    </div>
  );
}

export function CompanyDetailsModal({ company, onClose }) {
  const [imgError, setImgError] = useState(false);

  return (
    <div className="fixed inset-0 z-50 bg-white/80 backdrop-blur-none flex items-center justify-center">
      <div className="bg-white w-full max-w-4xl rounded-xl p-6 relative shadow-2xl border">
        <button
          onClick={onClose}
          className="absolute top-3 right-4 text-gray-600 text-xl hover:text-black"
        >
          &times;
        </button>

        {/* Header */}
        <div className="flex items-center gap-4 border-b pb-4">
          <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
            {!imgError ? (
              <img
                src={company.Logo}
                alt="Logo"
                className="w-full h-full object-cover"
                onError={() => setImgError(true)}
              />
            ) : (
              <span className="text-sm text-gray-500">Logo</span>
            )}
          </div>
          <div>
            <h2 className="text-xl font-bold">{company.companyName}</h2>
            <p className="text-sm text-gray-500">{company.companyDescription}</p>
            <p className="text-sm text-gray-500">{company.location}</p>
            <p className="text-sm text-gray-500">{company.employeeCount} Employees</p>
            <a
              href={company.website}
              className="text-blue-500 underline text-sm"
              target="_blank"
              rel="noopener noreferrer"
            >
              {company.website}
            </a>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-5 gap-4 my-6">
          <StatCard label="Total Active Jobs" value={company.totalActiveJobs} />
          <StatCard label="Total Applicants" value={company.totalApplicants} />
          <StatCard label="Total Hired" value={company.totalHired} />
          <StatCard label="Waiting for Approval" value={company.awaitedApproval} />
          <StatCard label="Other Stat" value="..." />
        </div>

        {/* Active Listings */}
        <div>
          <h3 className="font-semibold text-lg mb-2">Active Listing</h3>
          <table className="w-full table-auto text-sm">
            <thead>
              <tr className="text-left bg-gray-100">
                <th className="p-2">Title</th>
                <th className="p-2">Type</th>
                <th className="p-2">CTC</th>
                <th className="p-2">Stipend</th>
                <th className="p-2">Deadline</th>
              </tr>
            </thead>
            <tbody>
              {(company.activeListingsData || []).map((listing, i) => (
                <tr
                  key={i}
                  className={`${i % 2 === 0 ? 'bg-white' : 'bg-gray-50'} text-left`}
                >
                  <td className="p-2">{listing.title}</td>
                  <td className="p-2">{listing.type}</td>
                  <td className="px-4 py-4 text-gray-700">
                  {listing.ctc ? `${listing.ctc} LPA` : '-'}
                  </td>
                  <td className="px-4 py-4 text-gray-700">
                    {listing.stipend ? `${listing.stipend} INR ` : '-'}
                  </td>
                  <td className="p-2">{listing.deadline}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Small stat card
function StatCard({ label, value }) {
  return (
    <div className="bg-gray-100 rounded-lg p-4 text-center shadow">
      <p className="text-xl font-bold">{value}</p>
      <p className="text-sm text-gray-600">{label}</p>
    </div>
  );
}

export function JobTable({ jobs = [], onTogglePublish }) {
  if (jobs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10m8-10v10" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-500 mb-2">No job listings found</h3>
        <p className="text-sm text-gray-400">Job listings will appear here once they are created</p>
      </div>
    );
  }

  return (
    <div className="overflow-auto">
      <table className="min-w-full text-sm text-left border-collapse">
        <thead className="bg-gray-50 text-gray-700 sticky top-0">
          <tr>
            <th className="px-4 py-3 font-medium">Company</th>
            <th className="px-4 py-3 font-medium">Role</th>
            <th className="px-4 py-3 font-medium">Type</th>
            <th className="px-4 py-3 font-medium">CTC</th>
            <th className="px-4 py-3 font-medium">Stipend</th>
            <th className="px-4 py-3 font-medium">Deadline</th>
            <th className="px-4 py-3 font-medium">Status</th>
            {onTogglePublish && <th className="px-4 py-3 font-medium">Actions</th>}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {jobs.map((job, index) => (
            <tr
              key={index}
              className="hover:bg-gray-50 transition-colors"
            >
              <td className="px-4 py-4 font-medium text-gray-900">{job.companyName}</td>
              <td className="px-4 py-4 text-gray-700">{job.title}</td>
              <td className="px-4 py-4">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  job.type === 'Full-Time' 
                    ? 'bg-green-100 text-green-800' 
                    : job.type === 'Internship'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {job.type}
                </span>
              </td>
              <td className="px-4 py-4 text-gray-700">
                  {job.ctc ? `${job.ctc} LPA` : '-'}
                </td>
                <td className="px-4 py-4 text-gray-700">
                  {job.stipend ? `${job.stipend} INR ` : '-'}
                </td>
              <td className="px-4 py-4 text-gray-700">{job.deadline || job.application_deadline}</td>
              <td className="px-4 py-4">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  job.is_published 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {job.is_published ? 'Published' : 'To be Published'}
                </span>
              </td>
              {onTogglePublish && (
                <td className="px-4 py-4">
                  <button
                    onClick={() => onTogglePublish(job.id, job.is_published)}
                    className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                      job.is_published
                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {job.is_published ? 'Unpublish' : 'Publish'}
                  </button>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
export function JobFilters({
  typeFilter,
  setTypeFilter,
  minCTC,
  setMinCTC,
  maxCTC,
  setMaxCTC,
  minStipend,
  setMinStipend,
  maxStipend,
  setMaxStipend,
  deadlineFilter,
  setDeadlineFilter
}) {
  const resetFilters = () => {
    setTypeFilter('All');
    setMinCTC('');
    setMaxCTC('');
    setMinStipend('');
    setMaxStipend('');
    setDeadlineFilter('');
  };

  return (
    <div className="flex flex-wrap items-center gap-3">
      {/* Job Type */}
      <select
        value={typeFilter}
        onChange={(e) => setTypeFilter(e.target.value)}
        className="w-[100px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"
      >
        <option value="All">All</option>
        <option value="Full-Time">Full-Time</option>
        <option value="Internship">Intern</option>
      </select>

      {/* CTC */}
      <input
        type="number"
        placeholder="Min CTC"
        value={minCTC}
        onChange={(e) => setMinCTC(e.target.value)}
        className="w-[90px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"
      />
      <input
        type="number"
        placeholder="Max CTC"
        value={maxCTC}
        onChange={(e) => setMaxCTC(e.target.value)}
        className="w-[90px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"
      />

      {/* Stipend */}
      <input
        type="number"
        placeholder="Min Stipend"
        value={minStipend}
        onChange={(e) => setMinStipend(e.target.value)}
        className="w-[120px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"
      />
      <input
        type="number"
        placeholder="Max Stipend"
        value={maxStipend}
        onChange={(e) => setMaxStipend(e.target.value)}
        className="w-[120px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"
      />

      {/* Deadline */}
      <input
        type="date"
        value={deadlineFilter}
        onChange={(e) => setDeadlineFilter(e.target.value)}
        className="w-[140px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"
      />

      {/* Reset Filters Button */}
      <button
        onClick={resetFilters}
        className="px-3 py-2 text-sm font-medium bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex-shrink-0"
      >
        Reset
      </button>
    </div>
  );
}

export function PaginatedList({ items = [], renderItem, itemsPerPage = 15, searchQuery = '', chunkItems = false }) {
  const [currentPage, setCurrentPage] = useState(1);

  const isFiltering = searchQuery.trim() !== '';
  const filteredItems = items.filter(item =>
    item.companyName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const effectiveItems = isFiltering ? filteredItems : items;
  const totalPages = Math.ceil(effectiveItems.length / itemsPerPage);

  const paginatedItems = effectiveItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  return (
    <>
      {/* Render Mode */}
      {chunkItems ? (
        // 🔹 When chunkItems is true (like for JobTable)
        renderItem(paginatedItems, currentPage)
      ) : (
        // 🔹 Otherwise render each item individually (like cards)
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {paginatedItems.map(renderItem)}
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          <button
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded-lg border ${
              currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-blue-50'
            }`}
          >
            Prev
          </button>

          {[...Array(totalPages)].map((_, idx) => (
            <button
              key={idx}
              onClick={() => setCurrentPage(idx + 1)}
              className={`px-3 py-1 rounded-lg border ${
                currentPage === idx + 1 ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-blue-50'
              }`}
            >
              {idx + 1}
            </button>
          ))}

          <button
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded-lg border ${
              currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-blue-50'
            }`}
          >
            Next
          </button>
        </div>
      )}
    </>
  );
}
