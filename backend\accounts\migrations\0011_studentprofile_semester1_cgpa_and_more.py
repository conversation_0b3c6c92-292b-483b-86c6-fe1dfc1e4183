# Generated by Django 5.1.5 on 2025-07-01 15:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0010_studentprofile_address_studentprofile_city_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='studentprofile',
            name='semester1_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester1_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem1/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester1_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester2_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester2_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem2/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester2_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester3_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester3_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem3/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester3_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester4_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester4_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem4/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester4_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester5_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester5_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem5/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester5_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester6_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester6_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem6/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester6_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester7_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester7_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem7/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester7_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester8_cgpa',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester8_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem8/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='semester8_upload_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.DeleteModel(
            name='SemesterMarksheet',
        ),
    ]
