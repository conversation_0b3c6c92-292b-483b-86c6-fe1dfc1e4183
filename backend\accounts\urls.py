from django.urls import path, include, re_path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import StudentListView, StudentUpdateView, StudentDetailView
from .views import BulkStudentUpdateView
from .views import (
    StudentRegistrationView,
    LoginView,
    UserProfileView
)
from .views import StudentProfileViewSet
import re

# Set up the router without any prefix
router = DefaultRouter()
router.register(r'profiles', StudentProfileViewSet, basename='student-profile')

# Add the lookup_value_regex to allow 'me' as a valid lookup value
router.urls[0].pattern.regex = re.compile(r'profiles/(?P<pk>me|\d+)/')

urlpatterns = [
    path('register/student/', StudentRegistrationView.as_view(), name='register-student'),
    path('login/', LoginView.as_view(), name='login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token-refresh'),
    path('profile/', UserProfileView.as_view(), name='user-profile'),
    path('admin/bulk-update-students/', BulkStudentUpdateView.as_view(), name='bulk-update-students'),
    path('students/', StudentListView.as_view(), name='student-list'),
    path('students/<int:id>/', StudentDetailView.as_view(), name='student-detail'),
    path('students/<int:id>/update/', StudentUpdateView.as_view(), name='student-update'),

    # Include router URLs directly at this level
    path('', include(router.urls)),
]