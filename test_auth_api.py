#!/usr/bin/env python3
"""
Test script to verify authentication and PATCH operations work
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_authenticated_operations():
    print("Testing Authenticated Student Profile Operations")
    print("=" * 60)
    
    # First, let's try to get a token by logging in
    # We need to check if there are any existing users we can use
    
    # Test 1: Try to access profiles without authentication
    print("\n1. Testing access without authentication")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts/profiles/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Try to PATCH without authentication
    print("\n2. Testing PATCH without authentication")
    try:
        test_data = {"gpa": "8.5"}
        response = requests.patch(f"{BASE_URL}/api/accounts/profiles/1/", json=test_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Check what methods are allowed
    print("\n3. Testing allowed methods")
    try:
        response = requests.options(f"{BASE_URL}/api/accounts/profiles/1/")
        print(f"Status: {response.status_code}")
        print(f"Allowed methods: {response.headers.get('Allow', 'Not specified')}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Not specified')}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: Try the fallback endpoint
    print("\n4. Testing fallback endpoint PATCH")
    try:
        test_data = {"gpa": "8.5"}
        response = requests.patch(f"{BASE_URL}/api/accounts/students/1/update/", json=test_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 5: Check fallback endpoint methods
    print("\n5. Testing fallback endpoint methods")
    try:
        response = requests.options(f"{BASE_URL}/api/accounts/students/1/update/")
        print(f"Status: {response.status_code}")
        print(f"Allowed methods: {response.headers.get('Allow', 'Not specified')}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_authenticated_operations()
