import { NextResponse } from 'next/server';

export function middleware(request) {
  const url = request.nextUrl.clone();
  // const role = request.cookies.get('role')?.value;

  //Debug log (shows in terminal)
  console.log('[Middleware Triggered]');
  console.log('URL:', url.pathname);
  // console.log('Role:', role);

  // TODO: For testing purposes - comment out actual verification
  // Set the following for testing:
  // User Type: ADMIN (for admin access) or STUDENT (for student access)
  // College Name: Your college name (e.g., "AVV Chennai")
  
  /*
  // Block /profile unless logged in as student or admin
  if (url.pathname.startsWith('/profile') && role !== 'STUDENT' && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Admin-only access
  if (url.pathname.startsWith('/admin') && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Block student pages unless logged in as student or admin
  if ((url.pathname.startsWith('/myjobs') || 
       url.pathname.startsWith('/explore') || 
       url.pathname.startsWith('/jobpostings') || 
       url.pathname.startsWith('/employers') || 
       url.pathname.startsWith('/events') || 
       url.pathname.startsWith('/inbox')) && 
      role !== 'STUDENT' && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Block homepage `/` unless student or admin
  if (url.pathname === '/' && role !== 'STUDENT' && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }
  */

  // Otherwise allow the request
  return NextResponse.next();
}

// 🔁 Apply middleware to selected routes
export const config = {
  matcher: [
    '/',
    '/myjobs',
    '/explore',
    '/jobpostings',
    '/employers',
    '/events',
    '/inbox',
    '/profile/:path*',
    '/admin/:path*'
  ]
};
